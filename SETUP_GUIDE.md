# SmaTrendFollower Setup and Configuration Guide

## Prerequisites

### System Requirements
- **Operating System**: Windows 10/11, macOS 10.15+, or Linux (Ubuntu 20.04+)
- **.NET 8 SDK**: Download from [Microsoft .NET](https://dotnet.microsoft.com/download/dotnet/8.0)
- **Git**: For cloning the repository
- **Visual Studio Code** or **Visual Studio 2022**: Recommended IDEs

### Hardware Recommendations
- **CPU**: 4+ cores for parallel processing
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 10GB free space for data caching
- **Network**: Stable internet connection for API access

### Trading Account Requirements
- **Alpaca Markets Account**: [Sign up here](https://alpaca.markets/)
  - Paper trading account (free) for testing
  - Live trading account for production
  - API key and secret key
- **Polygon.io Account** (Optional): [Sign up here](https://polygon.io/)
  - Indices Starter plan for SPX/VIX data
  - Options Starter plan for options data

## Installation

### 1. Clone the Repository
```bash
git clone https://github.com/patco1/SmaTrendFollower.git
cd SmaTrendFollower
```

### 2. Verify .NET Installation
```bash
dotnet --version
# Should show 8.0.x or higher
```

### 3. Restore Dependencies
```bash
dotnet restore SmaTrendFollower.sln
```

### 4. Build the Solution
```bash
dotnet build SmaTrendFollower.sln
```

### 5. Run Tests (Optional)
```bash
dotnet test SmaTrendFollower.sln
```

## Configuration

### 1. Environment Variables Setup

#### Create .env File
```bash
# Copy the example file
cp SmaTrendFollower.Console/.env.example SmaTrendFollower.Console/.env
```

#### Configure Required Variables
Edit `SmaTrendFollower.Console/.env`:

```env
# Alpaca Markets API (Required)
APCA_API_KEY_ID=your_alpaca_api_key_here
APCA_API_SECRET_KEY=your_alpaca_secret_key_here
APCA_API_ENV=paper  # Use 'paper' for testing, 'live' for production

# Polygon.io API (Optional - for enhanced market data)
POLY_API_KEY=your_polygon_api_key_here

# Redis Configuration (Optional - for performance optimization)
REDIS_URL=localhost:6379
REDIS_DATABASE=0
# REDIS_PASSWORD=your_redis_password  # Uncomment if Redis requires authentication

# Discord Notifications (Optional)
DISCORD_BOT_TOKEN=your_discord_bot_token
DISCORD_CHANNEL_ID=your_discord_channel_id
```

### 2. Alpaca Markets Setup

#### Get API Credentials
1. Log in to [Alpaca Markets](https://app.alpaca.markets/)
2. Navigate to "API Keys" in the dashboard
3. Generate new API key and secret
4. Copy the credentials to your `.env` file

#### Paper vs Live Trading
- **Paper Trading**: Use `APCA_API_ENV=paper` for risk-free testing
- **Live Trading**: Use `APCA_API_ENV=live` for real money trading

⚠️ **Warning**: Always test thoroughly with paper trading before switching to live trading.

### 3. Polygon.io Setup (Optional)

#### Get API Key
1. Sign up at [Polygon.io](https://polygon.io/)
2. Choose appropriate plan:
   - **Indices Starter**: For SPX/VIX data ($99/month)
   - **Options Starter**: For options data ($199/month)
3. Copy API key to your `.env` file

#### Benefits of Polygon Integration
- Enhanced index data (SPX, VIX, DJI, NDX)
- Options data for advanced strategies
- Fallback data source for Alpaca throttling
- Higher rate limits for data requests

### 4. Redis Setup (Optional)

#### Install Redis
**Windows (using Chocolatey):**
```powershell
choco install redis-64
redis-server
```

**macOS (using Homebrew):**
```bash
brew install redis
brew services start redis
```

**Linux (Ubuntu):**
```bash
sudo apt update
sudo apt install redis-server
sudo systemctl start redis-server
```

#### Verify Redis Installation
```bash
redis-cli ping
# Should return: PONG
```

#### Benefits of Redis
- Pre-market cache warming for faster execution
- Real-time trading state management
- Improved performance for signal generation
- Reduced API call overhead

### 5. Discord Notifications Setup (Optional)

#### Create Discord Bot
1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Create new application
3. Go to "Bot" section and create bot
4. Copy bot token
5. Invite bot to your server with appropriate permissions

#### Configure Channel
1. Enable Developer Mode in Discord
2. Right-click on target channel
3. Copy Channel ID
4. Add to `.env` file

## Universe Configuration

### 1. Default Universe
The system uses a default universe of major ETFs and stocks:
- SPY (S&P 500 ETF)
- QQQ (NASDAQ ETF)
- AAPL (Apple)
- MSFT (Microsoft)
- NVDA (NVIDIA)

### 2. Custom Universe File
Create `universe.csv` in the project root:

```csv
SPY
QQQ
AAPL
MSFT
GOOGL
AMZN
TSLA
META
NVDA
NFLX
```

### 3. Dynamic Universe (Advanced)
Enable dynamic universe generation in configuration:

```csharp
// In Program.cs or configuration
services.Configure<UniverseOptions>(options =>
{
    options.MaxSymbols = 50;
    options.MinPrice = 10.00m;
    options.MinVolume = 1000000;
    options.MinVolatility = 0.02m;
});
```

## Running the Application

### 1. Basic Execution
```bash
# Navigate to console project
cd SmaTrendFollower.Console

# Run the application
dotnet run
```

### 2. Command Line Options
```bash
# Warm Redis cache before trading
dotnet run -- --warm-redis

# Test API connectivity
dotnet run -- --test-apis

# Run in dry-run mode (no actual trades)
dotnet run -- --dry-run
```

### 3. Scheduled Execution

#### Windows Task Scheduler
1. Open Task Scheduler
2. Create Basic Task
3. Set trigger for daily execution (e.g., 6:00 AM)
4. Set action to start program:
   - Program: `dotnet`
   - Arguments: `run --project SmaTrendFollower.Console`
   - Start in: `C:\path\to\SmaTrendFollower`

#### Linux/macOS Cron
```bash
# Edit crontab
crontab -e

# Add daily execution at 6:00 AM
0 6 * * 1-5 cd /path/to/SmaTrendFollower && dotnet run --project SmaTrendFollower.Console
```

## Production Deployment

### 1. Environment Preparation
```bash
# Create production directory
mkdir /opt/smatrendfollower
cd /opt/smatrendfollower

# Clone repository
git clone https://github.com/patco1/SmaTrendFollower.git .

# Set production environment
export ASPNETCORE_ENVIRONMENT=Production
```

### 2. Security Hardening
- Store API keys in secure key management system
- Use environment-specific configuration files
- Enable HTTPS for all API communications
- Implement proper logging and monitoring
- Set up automated backups for SQLite databases

### 3. Monitoring Setup
```bash
# Create log directory
mkdir -p /var/log/smatrendfollower

# Set up log rotation
sudo nano /etc/logrotate.d/smatrendfollower
```

Logrotate configuration:
```
/var/log/smatrendfollower/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 smatrendfollower smatrendfollower
}
```

### 4. Service Configuration (Linux)
Create systemd service file `/etc/systemd/system/smatrendfollower.service`:

```ini
[Unit]
Description=SmaTrendFollower Trading Bot
After=network.target

[Service]
Type=oneshot
User=smatrendfollower
WorkingDirectory=/opt/smatrendfollower
ExecStart=/usr/bin/dotnet run --project SmaTrendFollower.Console
Environment=ASPNETCORE_ENVIRONMENT=Production
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

Enable and start service:
```bash
sudo systemctl enable smatrendfollower.timer
sudo systemctl start smatrendfollower.timer
```

## Troubleshooting

### Common Issues

#### 1. API Connection Errors
```bash
# Test API connectivity
dotnet run -- --test-apis

# Check API credentials
echo $APCA_API_KEY_ID
echo $APCA_API_SECRET_KEY
```

#### 2. Database Issues
```bash
# Check SQLite database
ls -la *.db

# Reset cache database
rm stock_cache.db index_cache.db
```

#### 3. Redis Connection Issues
```bash
# Check Redis status
redis-cli ping

# Restart Redis service
sudo systemctl restart redis-server
```

#### 4. Permission Issues
```bash
# Fix file permissions
chmod +x SmaTrendFollower.Console
chown -R $USER:$USER .
```

### Logging and Diagnostics
- **Console Logs**: Real-time output during execution
- **File Logs**: Daily rolling logs in `logs/` directory
- **Error Logs**: Detailed error information with stack traces
- **Performance Metrics**: API call timing and cache hit rates

### Support Resources
- **GitHub Issues**: [Report bugs and feature requests](https://github.com/patco1/SmaTrendFollower/issues)
- **Documentation**: Comprehensive docs in repository
- **Community**: Discord server for user discussions
- **Professional Support**: Available for enterprise deployments

This setup guide provides everything needed to get SmaTrendFollower running in development, testing, and production environments.
