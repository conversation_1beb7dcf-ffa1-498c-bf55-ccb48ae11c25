# SmaTrendFollower Project Index

## 📋 Project Overview

**SmaTrendFollower** is a sophisticated .NET 8 algorithmic trading system implementing an SMA-following momentum strategy. The system features comprehensive risk management, real-time market data integration, and advanced caching for optimal performance.

## 🏗️ Project Structure

### Core Projects
```
SmaTrendFollower/
├── SmaTrendFollower.Console/          # Main console application
│   ├── Services/                      # Core trading services
│   ├── Models/                        # Data models and DTOs
│   ├── Data/                         # Database contexts and entities
│   ├── Factories/                    # Client factories (Alpaca, Polygon)
│   └── Program.cs                    # Application entry point
├── SmaTrendFollower.Tests/           # Comprehensive test suite
│   ├── Services/                     # Service unit tests
│   ├── Integration/                  # Integration tests
│   └── TestFixtures/                 # Test infrastructure
└── SmaTrendFollower.sln              # Solution file
```

### Documentation Files
```
Documentation/
├── README.md                         # Project overview and quick start
├── ARCHITECTURE.md                   # System architecture documentation
├── API_REFERENCE.md                  # Comprehensive API documentation
├── TRADING_STRATEGY.md               # Trading strategy details
├── SETUP_GUIDE.md                    # Setup and configuration guide
├── TESTING_GUIDE.md                  # Testing strategy and patterns
├── PERFORMANCE_MONITORING.md         # Performance optimization guide
├── REINDEXING_SUMMARY.md            # Project reindexing history
└── PROJECT_INDEX.md                  # This file
```

### Configuration Files
```
Configuration/
├── .env                              # Environment variables (create from .env.example)
├── .env.example                      # Environment template
├── universe.csv                      # Trading universe symbols
├── appsettings.json                  # Application configuration
└── appsettings.Development.json      # Development settings
```

### Build and Deployment
```
Build/
├── Makefile                          # Cross-platform build commands
├── setup-remote-environment.ps1     # Windows setup script
├── setup-remote-environment.sh      # Linux/macOS setup script
├── validate-environment.ps1         # Environment validation (Windows)
└── validate-environment-simple.ps1  # Simple validation script
```

## 🔧 Core Services Architecture

### Trading Services (Application Layer)
- **`ITradingService`** - Main orchestrator for trading cycles
- **`ISignalGenerator`** - SMA momentum signal generation
- **`IRiskManager`** - Position sizing and risk calculations
- **`ITradeExecutor`** - Order execution with stop-losses
- **`IPortfolioGate`** - Market condition gating (SPY SMA200)
- **`IStopManager`** - Trailing stop-loss management

### Data Services (Infrastructure Layer)
- **`IMarketDataService`** - Unified market data (Alpaca + Polygon)
- **`IStreamingDataService`** - Real-time data streaming
- **`IStockBarCacheService`** - SQLite historical data caching
- **`IRedisWarmingService`** - Pre-market cache warming
- **`ICacheManagementService`** - Cache optimization and metrics

### Enhanced Services (Optional Features)
- **`IMarketRegimeService`** - Market condition analysis
- **`IDynamicUniverseProvider`** - Dynamic symbol universe
- **`IVolatilityManager`** - VIX-based volatility analysis
- **`IOptionsStrategyManager`** - Options overlay strategies

### Client Factories
- **`IAlpacaClientFactory`** - Alpaca Markets API clients
- **`IPolygonClientFactory`** - Polygon.io API clients
- **`IRateLimitPolicyFactory`** - API rate limiting policies

## 📊 Data Models

### Core Trading Models
- **`TradingSignal`** - Signal with price, ATR, and momentum data
- **`MarketRegimeAnalysis`** - Market condition analysis results
- **`UniverseResult`** - Dynamic universe generation results
- **`CachedStockBar`** - SQLite cached bar entity
- **`IndexBar`** - Index data from Polygon API

### Configuration Models
- **`AlpacaOptions`** - Alpaca API configuration
- **`PolygonOptions`** - Polygon API configuration
- **`RedisOptions`** - Redis cache configuration
- **`TradingOptions`** - Trading strategy parameters

## 🧪 Testing Framework

### Test Categories
- **Unit Tests** - Individual component testing with mocks
- **Integration Tests** - Service interaction testing
- **Performance Tests** - Load and memory usage testing
- **API Tests** - External API integration testing

### Test Infrastructure
- **xUnit 2.6.1** - Testing framework
- **FluentAssertions 6.12.0** - Expressive assertions
- **Moq 4.20.69** - Mocking framework
- **TestFixture** - Shared test infrastructure

## 🚀 Quick Start Commands

### Development
```bash
# Clone and setup
git clone https://github.com/patco1/SmaTrendFollower.git
cd SmaTrendFollower

# Setup environment (Windows)
.\setup-remote-environment.ps1

# Setup environment (Linux/macOS)
./setup-remote-environment.sh

# Build and test
dotnet build SmaTrendFollower.sln
dotnet test SmaTrendFollower.sln

# Run application
dotnet run --project SmaTrendFollower.Console
```

### Using Makefile
```bash
# Build everything
make build

# Run tests
make test

# Run application
make run

# Quick environment check
make check

# Clean build artifacts
make clean
```

### Validation Scripts
```bash
# Validate environment (Windows)
.\validate-environment.ps1

# Simple validation
.\validate-environment-simple.ps1
```

## 📈 Performance Characteristics

### Execution Performance
- **Signal Generation**: 5-15 seconds for 500+ symbols
- **Trade Execution**: 2-5 seconds per trade
- **Cache Hit Ratio**: >90% for historical data
- **Memory Usage**: <500MB typical operation

### API Rate Limits
- **Alpaca Markets**: 200 requests/minute
- **Polygon.io**: 5 requests/second
- **Automatic Throttling**: Built-in rate limiting

## 🔐 Security Features

### API Key Management
- Environment variable storage
- Separate paper/live configurations
- No hardcoded credentials

### Data Protection
- SQLite encryption support
- Redis authentication
- HTTPS-only API communications

## 📦 Dependencies

### Core Dependencies
- **.NET 8.0** - Runtime framework
- **Alpaca.Markets 7.2.0** - Trading API
- **Skender.Stock.Indicators 2.6.1** - Technical analysis
- **Entity Framework Core 9.0.6** - Database ORM
- **StackExchange.Redis 2.8.41** - Redis client
- **Serilog** - Structured logging

### Development Dependencies
- **Microsoft.Extensions.Hosting** - Dependency injection
- **Microsoft.Extensions.Http.Polly** - HTTP retry policies
- **DotNetEnv 2.4.0** - Environment variable loading
- **System.Text.Json 9.0.6** - JSON serialization

## 🔍 Validation Status

### ✅ Project Consistency Validation
- **Namespaces**: All consistently use `SmaTrendFollower.*`
- **Project References**: Correct cross-project dependencies
- **Solution Structure**: Matches repository name and conventions
- **Build Status**: Solution builds successfully without errors
- **Test Coverage**: Comprehensive unit and integration tests

### ✅ Architecture Alignment
- **Single-Shot Execution**: Implemented (no scheduled services)
- **Dependency Injection**: Full DI container setup
- **Service Scoping**: Proper singleton/scoped service registration
- **Error Handling**: Comprehensive exception handling and logging

### ✅ User Preference Alignment
- **Risk Management**: 10bps per $100k cap implemented
- **Trading Strategy**: SMA-following momentum with universe screening
- **Data Sources**: Alpaca + Polygon integration
- **Caching Strategy**: SQLite + Redis hybrid caching
- **Notifications**: Discord integration ready

## 📚 Documentation Coverage

### Complete Documentation Set
1. **[README.md](README.md)** - Project overview and quick start
2. **[ARCHITECTURE.md](ARCHITECTURE.md)** - System architecture and design
3. **[API_REFERENCE.md](API_REFERENCE.md)** - Complete API documentation
4. **[TRADING_STRATEGY.md](TRADING_STRATEGY.md)** - Strategy implementation details
5. **[SETUP_GUIDE.md](SETUP_GUIDE.md)** - Installation and configuration
6. **[TESTING_GUIDE.md](TESTING_GUIDE.md)** - Testing patterns and practices
7. **[PERFORMANCE_MONITORING.md](PERFORMANCE_MONITORING.md)** - Performance optimization

### Additional Resources
- **Code Comments**: Comprehensive inline documentation
- **XML Documentation**: API documentation for IntelliSense
- **Example Configurations**: Sample .env and configuration files
- **Troubleshooting Guides**: Common issues and solutions

## 🎯 Next Steps

### Immediate Actions
1. **Configure Environment**: Set up .env file with API credentials
2. **Test Setup**: Run validation scripts to verify installation
3. **Paper Trading**: Start with paper trading to validate strategy
4. **Monitor Performance**: Use built-in monitoring and logging

### Optional Enhancements
1. **Redis Setup**: Install Redis for performance optimization
2. **Discord Notifications**: Configure Discord bot for trade alerts
3. **Market Regime Detection**: Enable advanced market analysis
4. **Options Strategies**: Implement options overlay strategies

## 📞 Support and Resources

### Development Resources
- **GitHub Repository**: [SmaTrendFollower](https://github.com/patco1/SmaTrendFollower)
- **Issue Tracking**: GitHub Issues for bug reports and feature requests
- **Documentation**: Comprehensive docs in repository

### API Documentation
- **Alpaca Markets**: [API Documentation](https://alpaca.markets/docs/)
- **Polygon.io**: [API Documentation](https://polygon.io/docs)
- **Technical Indicators**: [Skender.Stock.Indicators](https://github.com/DaveSkender/Stock.Indicators)

This project index provides a complete overview of the SmaTrendFollower system architecture, implementation, and usage patterns.
