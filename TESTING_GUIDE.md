# SmaTrendFollower Testing Guide

## Testing Philosophy

SmaTrendFollower employs a comprehensive testing strategy that ensures reliability, correctness, and performance of the trading system. The testing approach follows the testing pyramid with unit tests forming the foundation, integration tests validating component interactions, and end-to-end tests verifying complete workflows.

## Testing Architecture

```
                    ┌─────────────────────┐
                    │   End-to-End Tests  │
                    │   (Full Workflows)  │
                    └─────────────────────┘
                           ┌─────────────────────┐
                           │  Integration Tests  │
                           │ (Service Interactions)│
                           └─────────────────────┘
                    ┌─────────────────────────────────┐
                    │         Unit Tests              │
                    │    (Individual Components)      │
                    └─────────────────────────────────┘
```

## Test Project Structure

```
SmaTrendFollower.Tests/
├── Services/                    # Unit tests for services
│   ├── TradingServiceTests.cs
│   ├── SignalGeneratorTests.cs
│   ├── RiskManagerTests.cs
│   ├── TradeExecutorTests.cs
│   └── MarketDataServiceTests.cs
├── Integration/                 # Integration tests
│   ├── TradingServiceIntegrationTests.cs
│   ├── MarketDataIntegrationTests.cs
│   └── StreamingIntegrationTests.cs
├── Models/                      # Model and data structure tests
│   ├── TradingPrimitivesTests.cs
│   └── RedisModelsTests.cs
└── TestFixtures/               # Shared test infrastructure
    ├── TestFixture.cs
    └── MockDataProvider.cs
```

## Unit Testing Patterns

### 1. Service Testing with Mocks

#### Example: SignalGenerator Tests
```csharp
public class SignalGeneratorTests
{
    private readonly Mock<IMarketDataService> _mockMarketData;
    private readonly Mock<IUniverseProvider> _mockUniverse;
    private readonly Mock<ILogger<SignalGenerator>> _mockLogger;
    private readonly SignalGenerator _signalGenerator;

    public SignalGeneratorTests()
    {
        _mockMarketData = new Mock<IMarketDataService>();
        _mockUniverse = new Mock<IUniverseProvider>();
        _mockLogger = new Mock<ILogger<SignalGenerator>>();
        
        _signalGenerator = new SignalGenerator(
            _mockMarketData.Object,
            _mockUniverse.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task RunAsync_WithValidData_ShouldReturnSignals()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT", "GOOGL" };
        var testBars = CreateTestBars("AAPL", 250); // 250 days of data
        
        _mockUniverse.Setup(x => x.GetSymbolsAsync())
            .ReturnsAsync(symbols);
        
        _mockMarketData.Setup(x => x.GetStockBarsAsync(It.IsAny<string>(), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(CreateMockBarPage(testBars));

        // Act
        var signals = await _signalGenerator.RunAsync(5);

        // Assert
        signals.Should().NotBeNull();
        signals.Should().HaveCountLessOrEqualTo(5);
        signals.All(s => s.Price > 0).Should().BeTrue();
        signals.All(s => s.Atr > 0).Should().BeTrue();
    }

    [Theory]
    [InlineData(50)]   // Insufficient data for SMA200
    [InlineData(100)]  // Insufficient data for SMA200
    [InlineData(150)]  // Insufficient data for SMA200
    public async Task RunAsync_WithInsufficientData_ShouldSkipSymbol(int barCount)
    {
        // Arrange
        var symbols = new[] { "AAPL" };
        var testBars = CreateTestBars("AAPL", barCount);
        
        _mockUniverse.Setup(x => x.GetSymbolsAsync()).ReturnsAsync(symbols);
        _mockMarketData.Setup(x => x.GetStockBarsAsync(It.IsAny<string>(), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(CreateMockBarPage(testBars));

        // Act
        var signals = await _signalGenerator.RunAsync(10);

        // Assert
        signals.Should().BeEmpty();
    }
}
```

### 2. Risk Manager Testing

#### Example: Position Sizing Tests
```csharp
public class RiskManagerTests
{
    [Theory]
    [InlineData(100000, 150.00, 3.00, 333)] // $100k account, $150 stock, $3 ATR
    [InlineData(50000, 100.00, 2.00, 250)]  // $50k account, $100 stock, $2 ATR
    [InlineData(10000, 50.00, 1.00, 100)]   // $10k account, $50 stock, $1 ATR
    public async Task CalculateQuantityAsync_WithValidInputs_ShouldReturnCorrectSize(
        decimal accountEquity, decimal price, decimal atr, decimal expectedQuantity)
    {
        // Arrange
        var mockMarketData = new Mock<IMarketDataService>();
        var mockAccount = new Mock<IAccount>();
        
        mockAccount.Setup(x => x.Equity).Returns(accountEquity);
        mockMarketData.Setup(x => x.GetAccountAsync()).ReturnsAsync(mockAccount.Object);
        
        var riskManager = new RiskManager(mockMarketData.Object, Mock.Of<ILogger<RiskManager>>());
        var signal = new TradingSignal("AAPL", price, atr, 0.15m);

        // Act
        var quantity = await riskManager.CalculateQuantityAsync(signal);

        // Assert
        quantity.Should().Be(expectedQuantity);
    }

    [Fact]
    public async Task CalculateQuantityAsync_WithHighRisk_ShouldCapAt1000Dollars()
    {
        // Arrange - Large account where 1% > $1000
        var mockMarketData = new Mock<IMarketDataService>();
        var mockAccount = new Mock<IAccount>();
        
        mockAccount.Setup(x => x.Equity).Returns(200000m); // $200k account
        mockMarketData.Setup(x => x.GetAccountAsync()).ReturnsAsync(mockAccount.Object);
        
        var riskManager = new RiskManager(mockMarketData.Object, Mock.Of<ILogger<RiskManager>>());
        var signal = new TradingSignal("AAPL", 100m, 2m, 0.15m);

        // Act
        var quantity = await riskManager.CalculateQuantityAsync(signal);

        // Assert
        var riskUsed = quantity * signal.Atr * signal.Price;
        riskUsed.Should().BeLessOrEqualTo(1000m);
    }
}
```

### 3. Portfolio Gate Testing

#### Example: Market Condition Tests
```csharp
public class PortfolioGateTests
{
    [Fact]
    public async Task ShouldTradeAsync_WhenSpyAboveSma200_ShouldReturnTrue()
    {
        // Arrange
        var mockMarketData = new Mock<IMarketDataService>();
        var spyBars = CreateTrendingUpBars("SPY", 250);
        
        mockMarketData.Setup(x => x.GetStockBarsAsync("SPY", It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(CreateMockBarPage(spyBars));
        
        var portfolioGate = new PortfolioGate(mockMarketData.Object, Mock.Of<ILogger<PortfolioGate>>());

        // Act
        var shouldTrade = await portfolioGate.ShouldTradeAsync();

        // Assert
        shouldTrade.Should().BeTrue();
    }

    [Fact]
    public async Task ShouldTradeAsync_WhenSpyBelowSma200_ShouldReturnFalse()
    {
        // Arrange
        var mockMarketData = new Mock<IMarketDataService>();
        var spyBars = CreateTrendingDownBars("SPY", 250);
        
        mockMarketData.Setup(x => x.GetStockBarsAsync("SPY", It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(CreateMockBarPage(spyBars));
        
        var portfolioGate = new PortfolioGate(mockMarketData.Object, Mock.Of<ILogger<PortfolioGate>>());

        // Act
        var shouldTrade = await portfolioGate.ShouldTradeAsync();

        // Assert
        shouldTrade.Should().BeFalse();
    }
}
```

## Integration Testing

### 1. Trading Service Integration

#### Example: Complete Trading Cycle Test
```csharp
public class TradingServiceIntegrationTests : IClassFixture<TestFixture>
{
    private readonly IServiceProvider _serviceProvider;

    public TradingServiceIntegrationTests(TestFixture fixture)
    {
        _serviceProvider = fixture.ServiceProvider;
    }

    [Fact]
    public async Task ExecuteCycleAsync_WithMockedServices_ShouldCompleteSuccessfully()
    {
        // Arrange
        var tradingService = _serviceProvider.GetRequiredService<ITradingService>();

        // Act & Assert - Should not throw
        await tradingService.ExecuteCycleAsync();
    }

    [Fact]
    public async Task ExecuteCycleAsync_WithValidSignals_ShouldExecuteTrades()
    {
        // Arrange
        var mockSignalGenerator = new Mock<ISignalGenerator>();
        var mockRiskManager = new Mock<IRiskManager>();
        var mockPortfolioGate = new Mock<IPortfolioGate>();
        var mockTradeExecutor = new Mock<ITradeExecutor>();

        var signals = new[]
        {
            new TradingSignal("AAPL", 150m, 3m, 0.15m),
            new TradingSignal("MSFT", 300m, 5m, 0.12m)
        };

        mockPortfolioGate.Setup(x => x.ShouldTradeAsync()).ReturnsAsync(true);
        mockSignalGenerator.Setup(x => x.RunAsync(It.IsAny<int>())).ReturnsAsync(signals);
        mockRiskManager.Setup(x => x.CalculateQuantityAsync(It.IsAny<TradingSignal>())).ReturnsAsync(100m);

        var tradingService = new TradingService(
            mockSignalGenerator.Object,
            mockRiskManager.Object,
            mockPortfolioGate.Object,
            mockTradeExecutor.Object,
            Mock.Of<IStopManager>(),
            Mock.Of<ITradingSafetyGuard>(),
            Mock.Of<IMarketRegimeService>());

        // Act
        await tradingService.ExecuteCycleAsync();

        // Assert
        mockTradeExecutor.Verify(x => x.ExecuteTradeAsync(It.IsAny<TradingSignal>(), 100m, It.IsAny<CancellationToken>()), 
            Times.Exactly(2));
    }
}
```

### 2. Market Data Integration

#### Example: API Integration Tests
```csharp
[Collection("Integration")]
public class MarketDataIntegrationTests
{
    [Fact]
    [Trait("Category", "Integration")]
    public async Task GetStockBarsAsync_WithValidSymbol_ShouldReturnData()
    {
        // Arrange
        var serviceProvider = CreateServiceProvider();
        var marketDataService = serviceProvider.GetRequiredService<IMarketDataService>();
        
        var startDate = DateTime.UtcNow.AddDays(-30);
        var endDate = DateTime.UtcNow;

        // Act
        var bars = await marketDataService.GetStockBarsAsync("SPY", startDate, endDate);

        // Assert
        bars.Should().NotBeNull();
        bars.Items.Should().NotBeEmpty();
        bars.Items.Should().AllSatisfy(bar =>
        {
            bar.Symbol.Should().Be("SPY");
            bar.TimeUtc.Should().BeAfter(startDate);
            bar.TimeUtc.Should().BeBefore(endDate);
            bar.Close.Should().BeGreaterThan(0);
        });
    }

    [Fact]
    [Trait("Category", "Integration")]
    [Skip("Requires valid API credentials")]
    public async Task GetAccountAsync_WithValidCredentials_ShouldReturnAccount()
    {
        // This test requires actual API credentials
        // Skip by default to avoid failures in CI/CD
        
        var serviceProvider = CreateServiceProvider();
        var marketDataService = serviceProvider.GetRequiredService<IMarketDataService>();

        var account = await marketDataService.GetAccountAsync();

        account.Should().NotBeNull();
        account.Equity.Should().BeGreaterThan(0);
    }
}
```

## Test Data Generation

### 1. Synthetic Bar Data
```csharp
public static class TestDataGenerator
{
    public static List<IBar> CreateTestBars(string symbol, int count, decimal startPrice = 100m)
    {
        var bars = new List<IBar>();
        var random = new Random(42); // Fixed seed for reproducible tests
        var currentPrice = startPrice;
        var baseDate = DateTime.UtcNow.AddDays(-count);

        for (int i = 0; i < count; i++)
        {
            var change = (decimal)(random.NextDouble() - 0.5) * 0.04m; // ±2% daily change
            currentPrice *= (1 + change);
            
            var high = currentPrice * (1 + (decimal)random.NextDouble() * 0.02m);
            var low = currentPrice * (1 - (decimal)random.NextDouble() * 0.02m);
            var open = low + (high - low) * (decimal)random.NextDouble();
            
            bars.Add(new TestBar
            {
                Symbol = symbol,
                TimeUtc = baseDate.AddDays(i),
                Open = open,
                High = high,
                Low = low,
                Close = currentPrice,
                Volume = (ulong)(1000000 + random.Next(500000))
            });
        }

        return bars;
    }

    public static List<IBar> CreateTrendingUpBars(string symbol, int count)
    {
        // Generate bars with consistent upward trend
        var bars = new List<IBar>();
        var currentPrice = 100m;
        var baseDate = DateTime.UtcNow.AddDays(-count);

        for (int i = 0; i < count; i++)
        {
            var dailyGain = 0.001m + (decimal)(new Random(i).NextDouble() * 0.002m); // 0.1-0.3% daily
            currentPrice *= (1 + dailyGain);
            
            bars.Add(new TestBar
            {
                Symbol = symbol,
                TimeUtc = baseDate.AddDays(i),
                Open = currentPrice * 0.999m,
                High = currentPrice * 1.005m,
                Low = currentPrice * 0.995m,
                Close = currentPrice,
                Volume = 1000000
            });
        }

        return bars;
    }
}
```

### 2. Mock Data Providers
```csharp
public class MockMarketDataProvider
{
    public static Mock<IMarketDataService> CreateMockWithTrendingData()
    {
        var mock = new Mock<IMarketDataService>();
        
        // Setup account data
        var mockAccount = new Mock<IAccount>();
        mockAccount.Setup(x => x.Equity).Returns(100000m);
        mock.Setup(x => x.GetAccountAsync()).ReturnsAsync(mockAccount.Object);
        
        // Setup bar data for various symbols
        mock.Setup(x => x.GetStockBarsAsync(It.IsAny<string>(), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync((string symbol, DateTime start, DateTime end) =>
            {
                var bars = TestDataGenerator.CreateTrendingUpBars(symbol, 250);
                return new MockBarPage(bars);
            });
        
        return mock;
    }
}
```

## Performance Testing

### 1. Load Testing
```csharp
[Fact]
public async Task SignalGeneration_WithLargeUniverse_ShouldCompleteWithinTimeLimit()
{
    // Arrange
    var symbols = Enumerable.Range(1, 500).Select(i => $"STOCK{i}").ToArray();
    var mockUniverse = new Mock<IUniverseProvider>();
    mockUniverse.Setup(x => x.GetSymbolsAsync()).ReturnsAsync(symbols);
    
    var mockMarketData = MockMarketDataProvider.CreateMockWithTrendingData();
    var signalGenerator = new SignalGenerator(mockMarketData.Object, mockUniverse.Object, Mock.Of<ILogger<SignalGenerator>>());
    
    // Act
    var stopwatch = Stopwatch.StartNew();
    var signals = await signalGenerator.RunAsync(50);
    stopwatch.Stop();
    
    // Assert
    stopwatch.ElapsedMilliseconds.Should().BeLessThan(30000); // 30 seconds max
    signals.Should().HaveCountLessOrEqualTo(50);
}
```

### 2. Memory Usage Testing
```csharp
[Fact]
public async Task MarketDataService_WithLargeDataSet_ShouldNotExceedMemoryLimit()
{
    // Arrange
    var initialMemory = GC.GetTotalMemory(true);
    var marketDataService = CreateMarketDataService();
    
    // Act - Process large amount of data
    for (int i = 0; i < 100; i++)
    {
        await marketDataService.GetStockBarsAsync($"STOCK{i}", DateTime.UtcNow.AddYears(-1), DateTime.UtcNow);
    }
    
    GC.Collect();
    GC.WaitForPendingFinalizers();
    GC.Collect();
    
    var finalMemory = GC.GetTotalMemory(true);
    var memoryIncrease = finalMemory - initialMemory;
    
    // Assert - Memory increase should be reasonable
    memoryIncrease.Should().BeLessThan(100 * 1024 * 1024); // 100MB limit
}
```

## Test Configuration

### 1. Test Settings
```json
{
  "TestSettings": {
    "UseRealApis": false,
    "MockDataPath": "TestData/",
    "TestDatabasePath": ":memory:",
    "LogLevel": "Warning",
    "TimeoutSeconds": 30
  }
}
```

### 2. Test Categories
```csharp
// Fast unit tests
[Trait("Category", "Unit")]
[Trait("Speed", "Fast")]

// Integration tests requiring external dependencies
[Trait("Category", "Integration")]
[Trait("Speed", "Slow")]

// Tests requiring real API credentials
[Trait("Category", "Integration")]
[Trait("Requires", "ApiCredentials")]
```

## Continuous Integration

### 1. Test Pipeline
```yaml
# .github/workflows/test.yml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: 8.0.x
    
    - name: Restore dependencies
      run: dotnet restore
    
    - name: Build
      run: dotnet build --no-restore
    
    - name: Run unit tests
      run: dotnet test --no-build --filter "Category=Unit"
    
    - name: Run integration tests
      run: dotnet test --no-build --filter "Category=Integration&Requires!=ApiCredentials"
```

### 2. Test Coverage
```bash
# Install coverage tools
dotnet tool install --global dotnet-reportgenerator-globaltool

# Run tests with coverage
dotnet test --collect:"XPlat Code Coverage"

# Generate coverage report
reportgenerator -reports:"**/coverage.cobertura.xml" -targetdir:"coverage" -reporttypes:Html
```

This comprehensive testing guide ensures the SmaTrendFollower system maintains high quality and reliability through systematic testing at all levels.
