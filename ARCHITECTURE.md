# SmaTrendFollower Architecture Documentation

## Overview

SmaTrendFollower is a sophisticated .NET 8 trading system that implements an SMA-following momentum strategy. The architecture follows clean architecture principles with dependency injection, comprehensive testing, and modular design.

## System Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        SmaTrendFollower                        │
├─────────────────────────────────────────────────────────────────┤
│                     Presentation Layer                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Console App   │  │  Discord Bot    │  │   Web API       │ │
│  │   (Program.cs)  │  │  Notifications  │  │  (Future)       │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                     Application Layer                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ TradingService  │  │ SignalGenerator │  │  RiskManager    │ │
│  │ (Orchestrator)  │  │ (Strategy)      │  │  (Position)     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ TradeExecutor   │  │ PortfolioGate   │  │  StopManager    │ │
│  │ (Execution)     │  │ (Market Gate)   │  │  (Risk Control) │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                     Infrastructure Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ MarketDataSvc   │  │ StreamingData   │  │ CacheServices   │ │
│  │ (Alpaca+Polygon)│  │ (Real-time)     │  │ (Redis+SQLite)  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ AlpacaFactory   │  │ PolygonFactory  │  │ DatabaseCtx     │ │
│  │ (Trading API)   │  │ (Market Data)   │  │ (Persistence)   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                        Domain Layer                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ TradingSignal   │  │ MarketRegime    │  │ TradingPrimitives│ │
│  │ (Core Models)   │  │ (Market State)  │  │ (Value Objects) │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## Core Components

### 1. Trading Services (Application Layer)

#### TradingService
- **Purpose**: Main orchestrator for trading cycles
- **Responsibilities**: 
  - Coordinates all trading operations
  - Manages execution flow
  - Handles safety checks and market regime validation
- **Dependencies**: All core trading services

#### SignalGenerator
- **Purpose**: Generates trading signals using SMA momentum strategy
- **Strategy**: 
  - Universe screening (SPY + top-500 tickers)
  - Technical filters: Close > SMA50 && Close > SMA200 && ATR/Close < 3%
  - Ranking by 6-month return
  - Returns top N symbols
- **Dependencies**: MarketDataService, UniverseProvider

#### RiskManager
- **Purpose**: Position sizing and risk control
- **Algorithm**: 
  - Risk capital = min(account equity × 1%, $1000)
  - Position size = riskDollars / (ATR14 × price)
  - Maximum position constraint
- **Dependencies**: MarketDataService

#### TradeExecutor
- **Purpose**: Order execution and management
- **Pattern**: 
  - Limit-on-Open entry at lastClose × 1.002
  - GTC stop-loss at entry - 2×ATR
  - Cancel existing orders before new trades
- **Dependencies**: AlpacaClientFactory

#### PortfolioGate
- **Purpose**: Market condition gating
- **Logic**: Only trade when SPY close > SPY SMA200
- **Dependencies**: MarketDataService

### 2. Data Services (Infrastructure Layer)

#### MarketDataService
- **Purpose**: Unified market data interface
- **Data Sources**: 
  - Alpaca Markets: Account data, positions, stock/ETF bars
  - Polygon.io: Index data (SPX, VIX), options data, fallback bars
- **Features**: 
  - Automatic fallback mechanisms
  - Rate limiting and retry logic
  - Timestamp normalization (UTC)
  - Caching integration

#### StreamingDataService
- **Purpose**: Real-time market data streaming
- **Capabilities**: 
  - Live quotes and bars (Alpaca WebSocket)
  - Trade execution updates
  - Index/volatility triggers (Polygon WebSocket)
- **Features**: 
  - Automatic reconnection
  - Event-driven architecture
  - Connection health monitoring

#### Cache Services
- **StockBarCacheService**: SQLite-based historical bar caching
- **IndexCacheService**: Index data caching with compression
- **RedisWarmingService**: Pre-market cache warming for fast execution
- **CacheMetricsService**: Performance monitoring and optimization

### 3. Enhanced Services (Optional Features)

#### MarketRegimeService
- **Purpose**: Market condition analysis
- **Analysis**: SPY 100-day analysis using SMA slope, ATR volatility, return-to-drawdown ratio
- **Regimes**: TrendingUp, TrendingDown, Sideways, Volatile
- **Integration**: Skip trades during unfavorable regimes

#### DynamicUniverseProvider
- **Purpose**: Dynamic symbol universe generation
- **Features**: 
  - Redis caching with 24h TTL
  - Filtering by price, volume, volatility
  - Real-time universe updates
- **Criteria**: Price >$10, Volume >1M shares, Volatility >2% daily stddev

#### VolatilityManager & OptionsStrategyManager
- **Purpose**: Advanced volatility analysis and options strategies
- **Features**: VIX regime detection, options overlay strategies
- **Integration**: Enhanced risk management and hedging

## Data Flow

### Trading Cycle Flow
```
1. MarketSessionGuard → Validate trading allowed (weekdays)
2. TradingSafetyGuard → Safety validation checks
3. StopManager → Update trailing stops
4. PortfolioGate → Check SPY SMA200 condition
5. SignalGenerator → Generate trading signals
6. RiskManager → Calculate position sizes
7. TradeExecutor → Execute trades with stop-losses
```

### Data Integration Flow
```
1. MarketDataService → Fetch bars from Alpaca/Polygon
2. CacheService → Check SQLite cache for existing data
3. API Calls → Fetch missing data with rate limiting
4. Timestamp Normalization → Convert to UTC
5. Cache Storage → Store new data for future use
6. Return Unified Data → Consistent interface to consumers
```

## Technology Stack

### Core Framework
- **.NET 8**: Modern C# with nullable reference types
- **Microsoft.Extensions.Hosting**: Dependency injection and configuration
- **Microsoft.Extensions.Http**: HTTP client factory with Polly retry policies

### Market Data APIs
- **Alpaca.Markets SDK 7.2.0**: Trading and market data
- **Polygon.io REST API**: Index data and options
- **WebSocket Clients**: Real-time streaming data

### Data Storage
- **SQLite + Entity Framework Core**: Local data caching
- **Redis**: High-performance caching and session state
- **System.Text.Json**: Serialization and data exchange

### Technical Analysis
- **Skender.Stock.Indicators 2.6.1**: SMA, ATR, and other indicators
- **Custom Extensions**: Specialized calculations and filters

### Testing Framework
- **xUnit 2.6.1**: Unit and integration testing
- **FluentAssertions 6.12.0**: Expressive test assertions
- **Moq 4.20.69**: Mocking framework for dependencies

### Logging and Monitoring
- **Serilog**: Structured logging with console and file sinks
- **Custom Metrics**: Performance monitoring and cache statistics

## Configuration Management

### Environment Variables (.env)
```
# Alpaca Trading API
APCA_API_KEY_ID=your_alpaca_key
APCA_API_SECRET_KEY=your_alpaca_secret
APCA_API_ENV=paper  # or live

# Polygon Market Data
POLY_API_KEY=your_polygon_key

# Redis Cache (Optional)
REDIS_URL=localhost:6379
REDIS_DATABASE=0
REDIS_PASSWORD=your_password

# Discord Notifications (Optional)
DISCORD_BOT_TOKEN=your_bot_token
DISCORD_CHANNEL_ID=your_channel_id
```

### Dependency Injection Setup
- **Singleton Services**: Client factories, market data services, time providers
- **Scoped Services**: Trading services, signal generators, risk managers
- **Configuration**: Environment-based configuration with validation

## Security Considerations

### API Key Management
- Environment variable storage
- No hardcoded credentials
- Separate paper/live configurations

### Rate Limiting
- Alpaca: 200 requests/minute with exponential backoff
- Polygon: 5 requests/second with retry policies
- Circuit breaker patterns for API failures

### Data Validation
- Input sanitization for all external data
- Type safety with nullable reference types
- Comprehensive error handling and logging

## Performance Optimizations

### Caching Strategy
- **SQLite Cache**: 1-year historical bar retention
- **Redis Cache**: Live trading state with 24h TTL
- **Compression**: Data compression for storage efficiency
- **Bulk Operations**: Batch processing for database operations

### Connection Management
- **HTTP Client Factory**: Reusable HTTP connections
- **WebSocket Pooling**: Efficient real-time connections
- **Connection Health**: Automatic reconnection and monitoring

### Memory Management
- **Streaming Processing**: Large datasets processed in chunks
- **Disposal Patterns**: Proper resource cleanup
- **Garbage Collection**: Optimized object lifecycle management

## Monitoring and Observability

### Logging Strategy
- **Structured Logging**: JSON-formatted logs with Serilog
- **Log Levels**: Debug, Information, Warning, Error, Critical
- **Rolling Files**: Daily log files with 30-day retention
- **Performance Metrics**: Execution times and API call statistics

### Health Monitoring
- **API Health Checks**: Alpaca and Polygon connectivity
- **Cache Performance**: Hit rates and response times
- **Trading Metrics**: Signal generation and execution statistics
- **Error Tracking**: Exception logging and alerting

This architecture provides a robust, scalable, and maintainable foundation for algorithmic trading with comprehensive risk management and monitoring capabilities.
