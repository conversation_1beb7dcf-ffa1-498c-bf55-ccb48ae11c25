# SmaTrendFollower

A .NET 8 console application that implements an SMA-following momentum trading strategy using the Alpaca Markets API.

## Features

- **Single-Shot Execution**: Manual one-shot execution flow with market session guard
- **Universe Screening**: Screens SPY + top-500 tickers with SMA50/SMA200 filtering
- **Portfolio Gate**: SPY SMA200 check to determine if trading should occur
- **Risk Management**: 10bps per $100k equity cap with ATR-based position sizing
- **Trade Execution**: Limit-on-Open pattern with 2×ATR stop-loss orders
- **Comprehensive Logging**: Uses Serilog for console and file logging with daily rolling logs
- **Dependency Injection**: Clean architecture with scoped services for each trading cycle
- **Comprehensive Testing**: Unit tests with xUnit, FluentAssertions, and Moq

## Architecture

The application follows a clean architecture pattern with the following services:

### Core Trading Services
- **MarketSessionGuard**: Validates trading is allowed (weekdays only)
- **TradingService**: Orchestrates the complete trading cycle
- **SignalGenerator**: Universe screening with SPY + top-500 tickers, SMA filtering
- **PortfolioGate**: SPY SMA200 check to gate trading decisions
- **RiskManager**: 10bps per $100k cap with ATR-based position sizing
- **TradeExecutor**: Limit-on-Open + 2×ATR stop-loss execution pattern

### Data & Infrastructure Services
- **AlpacaClientFactory**: Creates and manages Alpaca API clients
- **PolygonClientFactory**: Creates and manages Polygon HTTP clients
- **MarketDataService**: Unified interface combining Alpaca + Polygon data sources
  - Account data, positions, live fills from Alpaca
  - Historical daily/minute bars with automatic fallback
  - Index data and options Greeks from Polygon
- **StreamingDataService**: Real-time websocket data streams
  - Live quotes and bars for equity symbols (Alpaca)
  - Trade execution updates and fills (Alpaca)
  - Index/volatility triggers (Polygon)
- **UniverseProvider**: Provides symbol universe from file or defaults

## Setup

### Prerequisites

- .NET 8 SDK
- Alpaca Markets account (paper or live)
- Polygon.io API key (optional, for index data like SPX, VIX)

### Configuration

1. Copy `.env.example` to `.env`:
   ```bash
   cp SmaTrendFollower.Console/.env.example SmaTrendFollower.Console/.env
   ```

2. Update the `.env` file with your credentials:
   ```
   # Alpaca credentials (required)
   APCA_API_KEY_ID=your_alpaca_api_key_here
   APCA_API_SECRET_KEY=your_alpaca_secret_key_here
   APCA_API_ENV=paper  # or live for real trading

   # Polygon credentials (optional, for index data like SPX, VIX)
   POLY_API_KEY=your_polygon_api_key_here

   # Redis configuration (optional, for cache warming)
   REDIS_URL=localhost:6379
   REDIS_DATABASE=0
   # REDIS_PASSWORD=your_redis_password  # if required
   ```

### Running the Application

```bash
# Build the solution
dotnet build SmaTrendFollower.sln

# Run the application
dotnet run --project SmaTrendFollower.Console

# Run tests
dotnet test SmaTrendFollower.sln

# Warm Redis cache (optional, for faster trading state access)
dotnet run --project SmaTrendFollower.Console -- --warm-redis
```

## Trading Strategy

The bot implements an SMA-following momentum strategy with universe screening:

### Signal Generation
- **Universe**: SPY + top-500 tickers from universe.csv or defaults
- **Filtering**: close > SMA50 && close > SMA200 && ATR/close < 3%
- **Ranking**: Ranked by 6-month return descending
- **Selection**: Top N symbols (default 10)

### Portfolio Gate
- **SPY SMA200 Check**: Only trade when SPY close > SPY SMA200
- **Market Session**: Only trade on weekdays (no weekends)

### Risk Management
- **Risk Capital**: min(account equity × 1%, $1000) - 10bps per $100k cap
- **Position Sizing**: quantity = riskDollars / (ATR14 × price)
- **Max Position**: quantity ≤ riskDollars / price

### Trade Execution
- **Entry**: Limit-on-Open at lastClose × 1.002
- **Stop Loss**: GTC stop at entry - 2×ATR14
- **Order Management**: Cancel existing orders before new trades

### Default Universe

The bot uses these symbols by default (when universe.csv is not found):
- SPY (S&P 500 ETF)
- QQQ (NASDAQ ETF)
- AAPL (Apple)
- MSFT (Microsoft)
- NVDA (NVIDIA)

Create a `universe.csv` file in the project root with one symbol per line to customize the universe.

## Market Data Integration

The bot uses a comprehensive market data system combining Alpaca and Polygon data sources with real-time streaming capabilities:

### Data Sources & Capabilities

#### Alpaca Markets
- **Account Data**: Real-time account information, equity, buying power
- **Positions**: Current holdings with real-time P&L tracking
- **Live Fills**: Recent trade executions and order status
- **Equity/ETF Data**: Daily and minute bars for tradeable symbols
- **Real-time Streaming**: Live quotes, bars, and trade updates via websocket
- **Fallback Support**: Automatic fallback to Polygon for throttled requests

#### Polygon.io
- **Index Data**: SPX, VIX, DJI, NDX real-time values and historical bars
- **Options Data**: Greeks (Delta, Gamma, Theta, Vega), Implied Volatility, Open Interest
- **VIX Term Structure**: Volatility term structure for hedging strategies
- **Fallback Provider**: Minute bars when Alpaca hits rate limits

### Historical Data Examples
```csharp
// Daily stock bars from Alpaca
var dailyBars = await marketDataService.GetStockBarsAsync("AAPL", startDate, endDate);

// Minute bars with automatic Polygon fallback on throttling
var minuteBars = await marketDataService.GetStockMinuteBarsAsync("AAPL", startDate, endDate);

// Multiple symbols with batch processing
var symbols = new[] { "SPY", "QQQ", "MSFT" };
var allBars = await marketDataService.GetStockBarsAsync(symbols, startDate, endDate);

// Index data from Polygon
var spxValue = await marketDataService.GetIndexValueAsync("I:SPX");
var vixBars = await marketDataService.GetIndexBarsAsync("I:VIX", startDate, endDate);
```

### Account & Portfolio Examples
```csharp
// Account information
var account = await marketDataService.GetAccountAsync();
Console.WriteLine($"Equity: {account.Equity:C}, Buying Power: {account.BuyingPower:C}");

// Current positions
var positions = await marketDataService.GetPositionsAsync();
foreach (var position in positions)
{
    Console.WriteLine($"{position.Symbol}: {position.Quantity} shares, P&L: {position.UnrealizedProfitLoss:C}");
}

// Recent fills/executions
var recentFills = await marketDataService.GetRecentFillsAsync(10);
```

### Options Data Examples
```csharp
// Get options chain for SPY
var spyOptions = await marketDataService.GetOptionsDataAsync("SPY");

// Get options for specific expiration
var weeklyOptions = await marketDataService.GetOptionsDataAsync("SPY", DateTime.Today.AddDays(7));

// VIX term structure for volatility analysis
var vixTerm = await marketDataService.GetVixTermStructureAsync();
```

### Real-time Streaming Examples
```csharp
// Set up streaming service
var streamingService = serviceProvider.GetRequiredService<IStreamingDataService>();

// Subscribe to events
streamingService.QuoteReceived += (sender, e) =>
    Console.WriteLine($"{e.Symbol}: Bid={e.BidPrice:C} Ask={e.AskPrice:C}");

streamingService.TradeUpdated += (sender, e) =>
    Console.WriteLine($"Trade: {e.Symbol} {e.Side} {e.Quantity} @ {e.Price:C}");

// Connect and subscribe
await streamingService.ConnectAlpacaStreamAsync();
await streamingService.SubscribeToQuotesAsync(new[] { "SPY", "QQQ", "AAPL" });
await streamingService.SubscribeToTradeUpdatesAsync();
```

### Supported Index Symbols (Polygon)
- `I:SPX` - S&P 500 Index
- `I:VIX` - CBOE Volatility Index
- `I:DJI` - Dow Jones Industrial Average
- `I:NDX` - NASDAQ 100 Index

### Error Handling & Resilience
- **Automatic Fallback**: Polygon minute bars when Alpaca throttles
- **Retry Logic**: Exponential backoff for transient failures
- **Connection Recovery**: Automatic reconnection for streaming data
- **Graceful Degradation**: Continue operation with partial data sources

### Clock Alignment & Timestamp Handling
- **Consistent UTC**: All timestamps normalized to UTC across data sources
- **Polygon Conversion**: Milliseconds-since-epoch converted via DateTimeOffset
- **Timezone Safety**: Proper handling of market hours and daylight saving
- **Data Mixing**: Seamless integration of Alpaca and Polygon timestamps

## Redis Cache Warming

The bot includes a Redis cache warming service that prepares live trading state before market open for faster execution:

### Features
- **Trailing Stop Levels**: Loads historical stop-loss data from SQLite into Redis
- **Signal Flags**: Initializes daily trading flags to prevent duplicate trades
- **Throttle Keys**: Sets up daily trading blocks for risk management
- **Fast Access**: Sub-millisecond retrieval during live trading

### Cache Keys
- `stop:{symbol}` - Trailing stop levels (e.g., `stop:AAPL`)
- `signal:{symbol}:{yyyymmdd}` - Daily signal flags (e.g., `signal:AAPL:20240620`)
- `block:{symbol}:{yyyymmdd}` - Daily throttle flags (e.g., `block:NFLX:20240620`)

### Usage
```bash
# Warm Redis cache manually
dotnet run --project SmaTrendFollower.Console -- --warm-redis

# Cache is automatically warmed during normal trading execution
dotnet run --project SmaTrendFollower.Console
```

### Configuration
Redis configuration is optional. If not configured, the service will gracefully skip cache warming:

```env
# Redis connection settings
REDIS_URL=localhost:6379
REDIS_DATABASE=0
REDIS_PASSWORD=your_password  # if required
```

### Benefits
- 🚀 **Faster Signal Evaluation**: Pre-loaded stop levels avoid database queries
- 🛡️ **Reliable Stop Behavior**: Consistent stop-loss tracking from first tick
- 🔁 **Reduced API Load**: Less database overhead during trading cycles
- 🧪 **Consistent Backtests**: Reproducible state for testing

### Data Persistence
- **SQLite Storage**: Historical trailing stops stored in `TrailingStops` table
- **Redis Cache**: Live trading state cached with 24-hour TTL
- **Automatic Sync**: Cache state persisted back to SQLite after trading

## Logging

Logs are written to:
- Console (real-time)
- `logs/alpaca-momentum-bot-YYYY-MM-DD.log` (daily rolling files, 30-day retention)

## Execution

The bot uses a single-shot execution model:
- Run manually via `dotnet run --project SmaTrendFollower.Console`
- Checks market session guard (weekdays only)
- Executes complete trading cycle and exits
- Suitable for scheduled execution via external scheduler (cron, Task Scheduler, etc.)

## Safety Features

- **Paper Trading**: Set `ALPACA_PAPER=true` for safe testing
- **Position Limits**: Prevents over-concentration
- **Market Hours Check**: Only trades when markets are open
- **Error Handling**: Comprehensive exception handling and logging
- **Dry Run Capability**: Can be easily modified for simulation mode

## Development

### Project Structure

```
SmaTrendFollower/
├── SmaTrendFollower.Console/           # Main console application
│   ├── Services/                       # Business logic services
│   ├── Models/                         # Data models and primitives
│   ├── Extensions/                     # Extension methods for indicators
│   ├── Data/                           # Database contexts and cache models
│   ├── Examples/                       # Usage examples
│   ├── Program.cs                      # Application entry point
│   └── .env.example                    # Environment variables template
├── SmaTrendFollower.Tests/             # Unit tests with xUnit
│   ├── Services/                       # Service unit tests
│   └── Integration/                    # Integration tests
├── README.md                           # This file
└── universe.csv                        # Optional symbol universe file
```

### Adding New Features

1. Create interfaces in the `Services` folder
2. Implement services with proper dependency injection
3. Register services in `Program.cs`
4. Add comprehensive unit tests
5. Update documentation

## Documentation

### 📚 Complete Documentation Suite

For comprehensive information about the SmaTrendFollower system:

#### Core Documentation
- **[PROJECT_INDEX.md](PROJECT_INDEX.md)** - Complete project overview and index
- **[ARCHITECTURE.md](ARCHITECTURE.md)** - System architecture and design patterns
- **[API_REFERENCE.md](API_REFERENCE.md)** - Comprehensive API documentation
- **[TRADING_STRATEGY.md](TRADING_STRATEGY.md)** - SMA momentum strategy details

#### Setup and Operations
- **[SETUP_GUIDE.md](SETUP_GUIDE.md)** - Installation and configuration guide
- **[TESTING_GUIDE.md](TESTING_GUIDE.md)** - Testing patterns and best practices
- **[PERFORMANCE_MONITORING.md](PERFORMANCE_MONITORING.md)** - Performance optimization

#### Project History
- **[REINDEXING_SUMMARY.md](REINDEXING_SUMMARY.md)** - Project reindexing and alignment summary

## Disclaimer

This software is for educational and research purposes only. Trading involves substantial risk of loss and is not suitable for all investors. Past performance does not guarantee future results. Always test thoroughly with paper trading before using real money.

## License

This project is licensed under the MIT License.
