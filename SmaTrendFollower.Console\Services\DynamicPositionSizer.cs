using Alpaca.Markets;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Advanced position sizing that adapts to market volatility and risk conditions.
/// Implements ATR-based sizing with dynamic risk adjustment.
/// </summary>
public sealed class DynamicPositionSizer : IPositionSizer
{
    private readonly IMarketDataService _marketDataService;
    private readonly IVolatilityFilter _volatilityFilter;
    private readonly ILogger<DynamicPositionSizer> _logger;
    private readonly PositionSizingConfig _config;

    public DynamicPositionSizer(
        IMarketDataService marketDataService,
        IVolatilityFilter volatilityFilter,
        ILogger<DynamicPositionSizer> logger,
        PositionSizingConfig? config = null)
    {
        _marketDataService = marketDataService;
        _volatilityFilter = volatilityFilter;
        _logger = logger;
        _config = config ?? new PositionSizingConfig();
    }

    /// <summary>
    /// Calculates optimal position size based on risk and market conditions
    /// </summary>
    public async Task<DynamicPositionSizing> CalculateSizingAsync(TradingSignal signal, decimal accountEquity)
    {
        try
        {
            // Get account information for available cash
            var account = await _marketDataService.GetAccountAsync();
            var availableCash = account.TradableCash;

            // Get market volatility context
            var marketVolatility = await _volatilityFilter.GetMarketVolatilityAsync();
            
            // Calculate base risk amount
            var baseRiskAmount = CalculateBaseRiskAmount(accountEquity);
            
            // Apply volatility adjustment
            var adjustedRiskAmount = ApplyVolatilityAdjustment(baseRiskAmount, marketVolatility);
            
            // Calculate position size based on ATR
            var atrBasedSize = CalculateAtrBasedSize(signal, adjustedRiskAmount);
            
            // Apply maximum position limits
            var maxPositionValue = Math.Min(availableCash * _config.MaxPositionPercent, 
                                          accountEquity * _config.MaxAccountRiskPercent);
            var maxShares = (int)(maxPositionValue / signal.Price);
            
            // Final position size
            var finalShares = Math.Min(atrBasedSize.Shares, maxShares);
            var positionValue = finalShares * signal.Price;
            var stopDistance = signal.Price - atrBasedSize.StopPrice;
            var riskAmount = finalShares * stopDistance;
            var riskPercent = accountEquity > 0 ? riskAmount / accountEquity : 0;

            var sizing = new DynamicPositionSizing(
                signal.Symbol,
                finalShares,
                signal.Price,
                atrBasedSize.StopPrice,
                positionValue,
                riskAmount,
                riskPercent,
                atrBasedSize.AtrMultiplier,
                marketVolatility.Regime,
                CalculateConfidenceScore(signal, marketVolatility),
                GetSizingReason(atrBasedSize, maxShares, marketVolatility)
            );

            _logger.LogInformation("Position sizing for {Symbol}: {Shares} shares @ {Price:F2}, Risk={Risk:F2} ({RiskPercent:P2}), Stop={Stop:F2}",
                signal.Symbol, finalShares, signal.Price, riskAmount, riskPercent, atrBasedSize.StopPrice);

            return sizing;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating position sizing for {Symbol}", signal.Symbol);
            
            // Return minimal safe position on error
            return new DynamicPositionSizing(
                signal.Symbol,
                0,
                signal.Price,
                signal.Price * 0.95m, // 5% stop loss
                0,
                0,
                0,
                2.0m,
                MarketVolatilityRegime.Unknown,
                0,
                "Error in calculation"
            );
        }
    }

    /// <summary>
    /// Calculates base risk amount based on account size and configuration
    /// </summary>
    private decimal CalculateBaseRiskAmount(decimal accountEquity)
    {
        // Progressive risk scaling: smaller accounts get higher risk percentage
        var riskPercent = accountEquity switch
        {
            < 10000 => _config.BaseRiskPercent * 1.5m,  // 1.5% for small accounts
            < 50000 => _config.BaseRiskPercent * 1.2m,  // 1.2% for medium accounts
            < 100000 => _config.BaseRiskPercent,        // 1.0% for large accounts
            _ => _config.BaseRiskPercent * 0.8m         // 0.8% for very large accounts
        };

        // Cap maximum risk amount
        var riskAmount = accountEquity * riskPercent;
        return Math.Min(riskAmount, _config.MaxRiskPerTrade);
    }

    /// <summary>
    /// Adjusts risk amount based on market volatility conditions
    /// </summary>
    private decimal ApplyVolatilityAdjustment(decimal baseRiskAmount, MarketVolatilityAnalysis marketVolatility)
    {
        var adjustment = marketVolatility.Regime switch
        {
            MarketVolatilityRegime.Low => 1.2m,      // Increase risk in low volatility
            MarketVolatilityRegime.Normal => 1.0m,   // Normal risk
            MarketVolatilityRegime.Elevated => 0.8m, // Reduce risk in elevated volatility
            MarketVolatilityRegime.High => 0.6m,     // Significantly reduce risk
            MarketVolatilityRegime.Crisis => 0.3m,   // Minimal risk in crisis
            _ => 0.5m                                 // Conservative default
        };

        var adjustedAmount = baseRiskAmount * adjustment;
        
        _logger.LogDebug("Volatility adjustment: {Regime} regime, {Adjustment:P0} adjustment, {Base:F2} -> {Adjusted:F2}",
            marketVolatility.Regime, adjustment - 1, baseRiskAmount, adjustedAmount);

        return adjustedAmount;
    }

    /// <summary>
    /// Calculates position size based on ATR and risk amount
    /// </summary>
    private AtrBasedSizing CalculateAtrBasedSize(TradingSignal signal, decimal riskAmount)
    {
        // Use multiple ATR multipliers based on market conditions
        var atrMultiplier = DetermineAtrMultiplier(signal);
        
        // Calculate stop price
        var stopDistance = signal.Atr * atrMultiplier;
        var stopPrice = signal.Price - stopDistance;
        
        // Ensure stop price is reasonable (not too close or too far)
        var minStopDistance = signal.Price * _config.MinStopPercent;
        var maxStopDistance = signal.Price * _config.MaxStopPercent;
        
        stopDistance = Math.Max(minStopDistance, Math.Min(maxStopDistance, stopDistance));
        stopPrice = signal.Price - stopDistance;
        
        // Calculate shares based on risk amount and stop distance
        var shares = stopDistance > 0 ? (int)(riskAmount / stopDistance) : 0;
        
        return new AtrBasedSizing(shares, stopPrice, atrMultiplier);
    }

    /// <summary>
    /// Determines appropriate ATR multiplier based on signal characteristics
    /// </summary>
    private decimal DetermineAtrMultiplier(TradingSignal signal)
    {
        // Base multiplier
        var multiplier = _config.BaseAtrMultiplier;
        
        // Adjust based on momentum strength
        if (signal.SixMonthReturn > 0.3m) // Strong momentum
            multiplier *= 0.8m; // Tighter stops for strong trends
        else if (signal.SixMonthReturn < 0.1m) // Weak momentum
            multiplier *= 1.2m; // Wider stops for weak trends
        
        // Adjust based on ATR/Price ratio (volatility)
        var atrPercent = signal.Price > 0 ? signal.Atr / signal.Price : 0;
        if (atrPercent > 0.04m) // High volatility
            multiplier *= 0.9m; // Slightly tighter stops
        else if (atrPercent < 0.015m) // Low volatility
            multiplier *= 1.1m; // Slightly wider stops
        
        // Ensure multiplier stays within reasonable bounds
        return Math.Max(_config.MinAtrMultiplier, Math.Min(_config.MaxAtrMultiplier, multiplier));
    }

    /// <summary>
    /// Calculates confidence score for the position sizing decision
    /// </summary>
    private decimal CalculateConfidenceScore(TradingSignal signal, MarketVolatilityAnalysis marketVolatility)
    {
        var score = 0.5m; // Base score
        
        // Momentum contribution (0-0.3)
        var momentumScore = Math.Min(0.3m, signal.SixMonthReturn * 0.5m);
        score += momentumScore;
        
        // Volatility contribution (0-0.2)
        var volatilityScore = marketVolatility.Regime switch
        {
            MarketVolatilityRegime.Low => 0.2m,
            MarketVolatilityRegime.Normal => 0.15m,
            MarketVolatilityRegime.Elevated => 0.1m,
            MarketVolatilityRegime.High => 0.05m,
            MarketVolatilityRegime.Crisis => 0m,
            _ => 0.05m
        };
        score += volatilityScore;
        
        // ATR quality contribution (0-0.1)
        var atrPercent = signal.Price > 0 ? signal.Atr / signal.Price : 0;
        var atrScore = atrPercent switch
        {
            >= 0.01m and <= 0.03m => 0.1m, // Ideal ATR range
            >= 0.005m and <= 0.05m => 0.05m, // Acceptable range
            _ => 0m // Too high or too low
        };
        score += atrScore;
        
        return Math.Max(0, Math.Min(1, score));
    }

    /// <summary>
    /// Gets human-readable reason for sizing decision
    /// </summary>
    private string GetSizingReason(AtrBasedSizing atrSizing, int maxShares, MarketVolatilityAnalysis marketVolatility)
    {
        var reasons = new List<string>();
        
        if (atrSizing.Shares == 0)
            reasons.Add("No position due to high risk");
        else if (atrSizing.Shares == maxShares)
            reasons.Add("Limited by maximum position size");
        else
            reasons.Add($"ATR-based sizing ({atrSizing.AtrMultiplier:F1}x multiplier)");
        
        reasons.Add($"{marketVolatility.Regime} volatility regime");
        
        return string.Join(", ", reasons);
    }
}

/// <summary>
/// Interface for position sizing
/// </summary>
public interface IPositionSizer
{
    Task<DynamicPositionSizing> CalculateSizingAsync(TradingSignal signal, decimal accountEquity);
}

/// <summary>
/// Configuration for position sizing
/// </summary>
public record PositionSizingConfig(
    decimal BaseRiskPercent = 0.01m,        // 1% base risk per trade
    decimal MaxRiskPerTrade = 1000m,        // Maximum $1000 risk per trade
    decimal MaxPositionPercent = 0.1m,      // Maximum 10% of cash per position
    decimal MaxAccountRiskPercent = 0.05m,  // Maximum 5% of account per position
    decimal BaseAtrMultiplier = 2.0m,       // Base ATR multiplier for stops
    decimal MinAtrMultiplier = 1.5m,        // Minimum ATR multiplier
    decimal MaxAtrMultiplier = 3.0m,        // Maximum ATR multiplier
    decimal MinStopPercent = 0.02m,         // Minimum 2% stop distance
    decimal MaxStopPercent = 0.08m          // Maximum 8% stop distance
);

/// <summary>
/// Position sizing calculation result
/// </summary>
public record DynamicPositionSizing(
    string Symbol,
    int Shares,
    decimal EntryPrice,
    decimal StopPrice,
    decimal PositionValue,
    decimal RiskAmount,
    decimal RiskPercent,
    decimal AtrMultiplier,
    MarketVolatilityRegime MarketRegime,
    decimal ConfidenceScore,
    string Reason
);

/// <summary>
/// ATR-based sizing intermediate result
/// </summary>
internal record AtrBasedSizing(
    int Shares,
    decimal StopPrice,
    decimal AtrMultiplier
);
