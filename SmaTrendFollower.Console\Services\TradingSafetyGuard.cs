using Alpaca.Markets;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Comprehensive trading safety guard implementation
/// </summary>
public sealed class TradingSafetyGuard : ITradingSafetyGuard
{
    private readonly IAlpacaClientFactory _clientFactory;
    private readonly ILogger<TradingSafetyGuard> _logger;
    private SafetyConfiguration _configuration;
    private readonly Dictionary<string, int> _dailyTradeCount = new();
    private readonly Dictionary<string, decimal> _dailyLoss = new();
    private DateTime _lastResetDate = DateTime.UtcNow.Date;

    public TradingSafetyGuard(IAlpacaClientFactory clientFactory, ILogger<TradingSafetyGuard> logger)
    {
        _clientFactory = clientFactory;
        _logger = logger;
        _configuration = new SafetyConfiguration(); // Default safe configuration
    }

    public async Task<SafetyCheckResult> ValidateTradeAsync(TradingSignal signal, decimal quantity)
    {
        try
        {
            ResetDailyCountersIfNeeded();

            // 1. Check if dry run mode is enabled
            if (_configuration.DryRunMode)
            {
                _logger.LogInformation("DRY RUN MODE: Would execute trade for {Symbol} with quantity {Quantity}", 
                    signal.Symbol, quantity);
                return new SafetyCheckResult(false, "Dry run mode enabled - no actual trades executed", SafetyLevel.Info);
            }

            // 2. Validate trading environment
            var envCheck = await ValidateEnvironmentAsync();
            if (!envCheck.IsAllowed)
                return envCheck;

            // 3. Check account equity
            var equityCheck = await ValidateAccountEquityAsync();
            if (!equityCheck.IsAllowed)
                return equityCheck;

            // 4. Check position limits
            var positionCheck = await ValidatePositionLimitsAsync();
            if (!positionCheck.IsAllowed)
                return positionCheck;

            // 5. Check daily trade limits
            var tradeCountCheck = ValidateDailyTradeLimit();
            if (!tradeCountCheck.IsAllowed)
                return tradeCountCheck;

            // 6. Check single trade value
            var tradeValueCheck = ValidateSingleTradeValue(signal, quantity);
            if (!tradeValueCheck.IsAllowed)
                return tradeValueCheck;

            // 7. Check position size percentage
            var positionSizeCheck = await ValidatePositionSizeAsync(signal, quantity);
            if (!positionSizeCheck.IsAllowed)
                return positionSizeCheck;

            // 8. Check daily loss limits
            var dailyLossCheck = ValidateDailyLossLimit();
            if (!dailyLossCheck.IsAllowed)
                return dailyLossCheck;

            // 9. Require confirmation for live trading
            if (_configuration.RequireConfirmation && await IsLiveEnvironmentAsync())
            {
                return new SafetyCheckResult(false, 
                    "Live trading requires manual confirmation. Use --confirm flag or disable RequireConfirmation.", 
                    SafetyLevel.Warning);
            }

            // All checks passed
            IncrementDailyTradeCount();
            _logger.LogInformation("Safety validation passed for {Symbol} trade", signal.Symbol);
            return new SafetyCheckResult(true, "All safety checks passed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during safety validation for {Symbol}", signal.Symbol);
            return new SafetyCheckResult(false, $"Safety validation error: {ex.Message}", SafetyLevel.Error);
        }
    }

    public async Task<SafetyCheckResult> ValidateTradingCycleAsync()
    {
        try
        {
            ResetDailyCountersIfNeeded();

            // Check if trading is allowed at all
            var envCheck = await ValidateEnvironmentAsync();
            if (!envCheck.IsAllowed)
                return envCheck;

            var equityCheck = await ValidateAccountEquityAsync();
            if (!equityCheck.IsAllowed)
                return equityCheck;

            var dailyLossCheck = ValidateDailyLossLimit();
            if (!dailyLossCheck.IsAllowed)
                return dailyLossCheck;

            _logger.LogInformation("Trading cycle safety validation passed");
            return new SafetyCheckResult(true, "Trading cycle validation passed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during trading cycle validation");
            return new SafetyCheckResult(false, $"Trading cycle validation error: {ex.Message}", SafetyLevel.Error);
        }
    }

    public SafetyConfiguration GetConfiguration() => _configuration;

    public void UpdateConfiguration(SafetyConfiguration config)
    {
        _configuration = config;
        _logger.LogInformation("Safety configuration updated: DryRun={DryRun}, MaxDailyLoss={MaxDailyLoss:C}, " +
                             "MaxPositions={MaxPositions}, RequireConfirmation={RequireConfirmation}",
            config.DryRunMode, config.MaxDailyLoss, config.MaxPositions, config.RequireConfirmation);
    }

    private async Task<SafetyCheckResult> ValidateEnvironmentAsync()
    {
        var isLive = await IsLiveEnvironmentAsync();
        
        return _configuration.AllowedEnvironment switch
        {
            TradingEnvironment.Paper when isLive => 
                new SafetyCheckResult(false, "Live trading not allowed - paper trading only", SafetyLevel.Critical),
            TradingEnvironment.Live when !isLive => 
                new SafetyCheckResult(false, "Paper trading not allowed - live trading only", SafetyLevel.Warning),
            _ => new SafetyCheckResult(true, $"Environment validation passed ({(isLive ? "Live" : "Paper")})")
        };
    }

    private async Task<bool> IsLiveEnvironmentAsync()
    {
        // Primary check: Use environment variable as the source of truth
        var apiEnv = Environment.GetEnvironmentVariable("APCA_API_ENV");
        var isLiveByEnv = !string.Equals(apiEnv, "paper", StringComparison.OrdinalIgnoreCase);

        _logger.LogDebug("Environment detection: APCA_API_ENV={ApiEnv}, IsLiveByEnv={IsLiveByEnv}",
            apiEnv, isLiveByEnv);

        try
        {
            // Secondary check: Verify with API call if possible
            using var tradingClient = _clientFactory.CreateTradingClient();
            var account = await tradingClient.GetAccountAsync();

            _logger.LogDebug("Environment verification: AccountId={AccountId}", account.AccountId);

            // Return the environment variable result since it's the authoritative source
            return isLiveByEnv;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "API call failed during environment verification, using environment variable result");

            // If API call fails, trust the environment variable
            // This handles cases where credentials might not work with the configured environment
            return isLiveByEnv;
        }
    }

    private async Task<SafetyCheckResult> ValidateAccountEquityAsync()
    {
        var rateLimitHelper = _clientFactory.GetRateLimitHelper();

        try
        {
            return await rateLimitHelper.ExecuteAsync(async () =>
            {
                using var tradingClient = _clientFactory.CreateTradingClient();
                var account = await tradingClient.GetAccountAsync();

                var equity = account.Equity ?? 0m;

                if (equity < _configuration.MinAccountEquity)
                {
                    return new SafetyCheckResult(false,
                        $"Account equity {equity:C} below minimum required {_configuration.MinAccountEquity:C}",
                        SafetyLevel.Critical);
                }

                return new SafetyCheckResult(true, $"Account equity validation passed: {equity:C}");
            }, "ValidateAccountEquity");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Account equity validation failed due to API error");

            // In dry run mode or when API fails, we can be more lenient
            if (_configuration.DryRunMode)
            {
                _logger.LogInformation("Dry run mode: Skipping account equity validation due to API error");
                return new SafetyCheckResult(true, "Account equity validation skipped (dry run mode)");
            }

            // For live trading, API failures are critical
            return new SafetyCheckResult(false,
                $"Account equity validation failed: {ex.Message}",
                SafetyLevel.Critical);
        }
    }

    private async Task<SafetyCheckResult> ValidatePositionLimitsAsync()
    {
        var rateLimitHelper = _clientFactory.GetRateLimitHelper();
        
        return await rateLimitHelper.ExecuteAsync(async () =>
        {
            using var tradingClient = _clientFactory.CreateTradingClient();
            var positions = await tradingClient.ListPositionsAsync();
            
            var openPositions = positions.Count(p => p.Quantity != 0);
            
            if (openPositions >= _configuration.MaxPositions)
            {
                return new SafetyCheckResult(false, 
                    $"Maximum positions reached: {openPositions}/{_configuration.MaxPositions}", 
                    SafetyLevel.Warning);
            }
            
            return new SafetyCheckResult(true, $"Position limit validation passed: {openPositions}/{_configuration.MaxPositions}");
        }, "ValidatePositionLimits");
    }

    private SafetyCheckResult ValidateDailyTradeLimit()
    {
        var today = DateTime.UtcNow.Date.ToString("yyyy-MM-dd");
        var todayCount = _dailyTradeCount.GetValueOrDefault(today, 0);
        
        if (todayCount >= _configuration.MaxDailyTrades)
        {
            return new SafetyCheckResult(false, 
                $"Daily trade limit reached: {todayCount}/{_configuration.MaxDailyTrades}", 
                SafetyLevel.Warning);
        }
        
        return new SafetyCheckResult(true, $"Daily trade limit validation passed: {todayCount}/{_configuration.MaxDailyTrades}");
    }

    private SafetyCheckResult ValidateSingleTradeValue(TradingSignal signal, decimal quantity)
    {
        var tradeValue = signal.Price * quantity;
        
        if (tradeValue > _configuration.MaxSingleTradeValue)
        {
            return new SafetyCheckResult(false, 
                $"Trade value {tradeValue:C} exceeds maximum {_configuration.MaxSingleTradeValue:C}", 
                SafetyLevel.Warning);
        }
        
        return new SafetyCheckResult(true, $"Trade value validation passed: {tradeValue:C}");
    }

    private async Task<SafetyCheckResult> ValidatePositionSizeAsync(TradingSignal signal, decimal quantity)
    {
        var rateLimitHelper = _clientFactory.GetRateLimitHelper();
        
        return await rateLimitHelper.ExecuteAsync(async () =>
        {
            using var tradingClient = _clientFactory.CreateTradingClient();
            var account = await tradingClient.GetAccountAsync();
            
            var equity = account.Equity ?? 0m;
            var positionValue = signal.Price * quantity;
            var positionPercent = equity > 0 ? (positionValue / equity) : 0;
            
            if (positionPercent > _configuration.MaxPositionSizePercent)
            {
                return new SafetyCheckResult(false, 
                    $"Position size {positionPercent:P2} exceeds maximum {_configuration.MaxPositionSizePercent:P2}", 
                    SafetyLevel.Warning);
            }
            
            return new SafetyCheckResult(true, $"Position size validation passed: {positionPercent:P2}");
        }, "ValidatePositionSize");
    }

    private SafetyCheckResult ValidateDailyLossLimit()
    {
        var today = DateTime.UtcNow.Date.ToString("yyyy-MM-dd");
        var todayLoss = _dailyLoss.GetValueOrDefault(today, 0m);
        
        if (Math.Abs(todayLoss) >= _configuration.MaxDailyLoss)
        {
            return new SafetyCheckResult(false, 
                $"Daily loss limit reached: {Math.Abs(todayLoss):C}/{_configuration.MaxDailyLoss:C}", 
                SafetyLevel.Critical);
        }
        
        return new SafetyCheckResult(true, $"Daily loss limit validation passed: {Math.Abs(todayLoss):C}/{_configuration.MaxDailyLoss:C}");
    }

    private void ResetDailyCountersIfNeeded()
    {
        var today = DateTime.UtcNow.Date;
        if (today > _lastResetDate)
        {
            _dailyTradeCount.Clear();
            _dailyLoss.Clear();
            _lastResetDate = today;
            _logger.LogInformation("Daily safety counters reset for {Date}", today);
        }
    }

    private void IncrementDailyTradeCount()
    {
        var today = DateTime.UtcNow.Date.ToString("yyyy-MM-dd");
        _dailyTradeCount[today] = _dailyTradeCount.GetValueOrDefault(today, 0) + 1;
    }
}
