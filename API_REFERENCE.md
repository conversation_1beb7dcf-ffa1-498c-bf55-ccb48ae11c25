# SmaTrendFollower API Reference

## Core Trading Interfaces

### ITradingService
Main orchestrator for trading operations.

```csharp
public interface ITradingService
{
    Task ExecuteCycleAsync(CancellationToken cancellationToken = default);
}
```

**Methods:**
- `ExecuteCycleAsync()`: Executes a complete trading cycle including signal generation, risk management, and trade execution.

### ISignalGenerator
Generates trading signals using SMA momentum strategy.

```csharp
public interface ISignalGenerator
{
    Task<IEnumerable<TradingSignal>> RunAsync(int maxSignals = 10, CancellationToken cancellationToken = default);
}
```

**Methods:**
- `RunAsync(maxSignals, cancellationToken)`: Generates up to `maxSignals` trading signals based on SMA momentum strategy.

**Strategy Details:**
- Universe: SPY + top-500 tickers
- Filters: Close > SMA50 && Close > SMA200 && ATR/Close < 3%
- Ranking: 6-month total return descending
- Output: Top N symbols with TradingSignal objects

### IRiskManager
Manages position sizing and risk calculations.

```csharp
public interface IRiskManager
{
    Task<decimal> CalculateQuantityAsync(TradingSignal signal, CancellationToken cancellationToken = default);
}
```

**Methods:**
- `CalculateQuantityAsync(signal, cancellationToken)`: Calculates position size based on risk management rules.

**Risk Algorithm:**
- Risk Capital: min(account equity × 1%, $1000) - 10bps per $100k cap
- Position Size: riskDollars / (ATR14 × price)
- Constraint: quantity ≤ riskDollars / price

### ITradeExecutor
Executes trades with stop-loss orders.

```csharp
public interface ITradeExecutor
{
    Task ExecuteTradeAsync(TradingSignal signal, decimal quantity, CancellationToken cancellationToken = default);
}
```

**Methods:**
- `ExecuteTradeAsync(signal, quantity, cancellationToken)`: Executes trade with Limit-on-Open entry and stop-loss.

**Execution Pattern:**
- Cancel existing orders for symbol
- Submit Limit-on-Open Buy at lastClose × 1.002
- Place GTC stop-loss Sell at entry - 2×ATR

### IPortfolioGate
Market condition gating for trading decisions.

```csharp
public interface IPortfolioGate
{
    Task<bool> ShouldTradeAsync(CancellationToken cancellationToken = default);
}
```

**Methods:**
- `ShouldTradeAsync(cancellationToken)`: Returns true if market conditions allow trading (SPY close > SPY SMA200).

## Market Data Interfaces

### IMarketDataService
Unified market data interface combining Alpaca and Polygon sources.

```csharp
public interface IMarketDataService
{
    // Historical Data
    Task<IPage<IBar>> GetStockBarsAsync(string symbol, DateTime startDate, DateTime endDate);
    Task<IPage<IBar>> GetStockMinuteBarsAsync(string symbol, DateTime startDate, DateTime endDate);
    Task<IDictionary<string, IPage<IBar>>> GetStockBarsAsync(IEnumerable<string> symbols, DateTime startDate, DateTime endDate);
    
    // Account & Positions
    Task<IAccount> GetAccountAsync();
    Task<IReadOnlyList<IPosition>> GetPositionsAsync();
    Task<IReadOnlyList<IOrder>> GetRecentFillsAsync(int limitCount = 100);
    
    // Index Data (Polygon)
    Task<decimal?> GetIndexValueAsync(string indexSymbol);
    Task<IEnumerable<IndexBar>> GetIndexBarsAsync(string indexSymbol, DateTime startDate, DateTime endDate);
    
    // Options Data (Polygon)
    Task<IEnumerable<OptionData>> GetOptionsDataAsync(string underlyingSymbol, DateTime? expirationDate = null);
    Task<IEnumerable<VixTermData>> GetVixTermStructureAsync();
}
```

**Data Sources:**
- **Alpaca Markets**: Account data, positions, stock/ETF bars, live fills
- **Polygon.io**: Index data (SPX, VIX), options data, fallback bars

**Features:**
- Automatic fallback from Alpaca to Polygon for throttled requests
- Timestamp normalization to UTC across all sources
- Comprehensive error handling and retry logic

### IStreamingDataService
Real-time market data streaming.

```csharp
public interface IStreamingDataService
{
    // Connection Management
    Task ConnectAlpacaStreamAsync();
    Task ConnectPolygonStreamAsync();
    Task DisconnectAsync();
    
    // Subscriptions
    Task SubscribeToQuotesAsync(IEnumerable<string> symbols);
    Task SubscribeToTradeUpdatesAsync();
    Task SubscribeToIndexUpdatesAsync(IEnumerable<string> indexSymbols);
    
    // Events
    event EventHandler<QuoteEventArgs> QuoteReceived;
    event EventHandler<TradeEventArgs> TradeUpdated;
    event EventHandler<IndexEventArgs> IndexUpdated;
}
```

**Capabilities:**
- Live quotes and bars via Alpaca WebSocket
- Trade execution updates and fills
- Index/volatility triggers via Polygon WebSocket
- Automatic reconnection and health monitoring

## Cache and Data Management

### IStockBarCacheService
SQLite-based historical bar caching.

```csharp
public interface IStockBarCacheService
{
    Task<List<CachedStockBar>> GetCachedBarsAsync(string symbol, string timeFrame, DateTime startDate, DateTime endDate);
    Task CacheBarsAsync(string symbol, string timeFrame, IEnumerable<IBar> bars);
    Task<(DateTime? earliestDate, DateTime? latestDate)> GetCachedDateRangeAsync(string symbol, string timeFrame);
    Task CleanupOldDataAsync(TimeSpan retentionPeriod);
}
```

**Features:**
- 1-year historical bar retention
- Bulk insert optimization
- Automatic cleanup of old data
- Performance metrics and monitoring

### IRedisWarmingService
Pre-market cache warming for fast execution.

```csharp
public interface IRedisWarmingService
{
    Task WarmCacheAsync(CancellationToken cancellationToken = default);
    Task PersistRedisStateAsync(CancellationToken cancellationToken = default);
    Task ClearCacheAsync(CancellationToken cancellationToken = default);
}
```

**Cache Keys:**
- `stop:{symbol}`: Trailing stop levels
- `signal:{symbol}:{yyyymmdd}`: Daily signal flags
- `block:{symbol}:{yyyymmdd}`: Daily throttle flags

## Enhanced Services

### IMarketRegimeService
Market condition analysis and regime detection.

```csharp
public interface IMarketRegimeService
{
    Task<MarketRegimeAnalysis> DetectRegimeAsync(string symbol = "SPY", CancellationToken cancellationToken = default);
    Task<MarketRegimeAnalysis> GetCachedRegimeAsync(string symbol = "SPY", CancellationToken cancellationToken = default);
}
```

**Regimes:**
- `TrendingUp`: Favorable for long positions
- `TrendingDown`: Unfavorable market conditions
- `Sideways`: Range-bound market
- `Volatile`: High volatility regime

### IDynamicUniverseProvider
Dynamic symbol universe generation with filtering.

```csharp
public interface IDynamicUniverseProvider
{
    Task<UniverseResult> GetCachedUniverseAsync(CancellationToken cancellationToken = default);
    Task<UniverseResult> BuildUniverseAsync(UniverseFilterCriteria criteria, CancellationToken cancellationToken = default);
    Task<UniverseResult> RefreshUniverseAsync(CancellationToken cancellationToken = default);
}
```

**Filter Criteria:**
- Minimum price: >$10
- Minimum volume: >1M shares daily
- Volatility: >2% daily standard deviation
- Maximum symbols: Configurable limit

## Core Models

### TradingSignal
Represents a trading signal with technical analysis data.

```csharp
public readonly record struct TradingSignal(
    string Symbol,
    decimal Price,
    decimal Atr,
    decimal SixMonthReturn
);
```

### MarketRegimeAnalysis
Market regime analysis results.

```csharp
public class MarketRegimeAnalysis
{
    public MarketRegime Regime { get; set; }
    public DateTime DetectedAt { get; set; }
    public decimal SmaSlope { get; set; }
    public decimal AverageAtr { get; set; }
    public decimal ReturnToDrawdownRatio { get; set; }
    public decimal Confidence { get; set; }
    public string Metadata { get; set; }
}
```

### UniverseResult
Dynamic universe generation results.

```csharp
public class UniverseResult
{
    public List<string> Symbols { get; set; }
    public DateTime GeneratedAt { get; set; }
    public int CandidateCount { get; set; }
    public int QualifiedCount { get; set; }
    public UniverseFilterCriteria FilterCriteria { get; set; }
    public UniverseMetrics Metrics { get; set; }
}
```

## Client Factories

### IAlpacaClientFactory
Creates and manages Alpaca API clients.

```csharp
public interface IAlpacaClientFactory
{
    IAlpacaTradingClient CreateTradingClient();
    IAlpacaDataClient CreateDataClient();
    IAlpacaStreamingClient CreateStreamingClient();
}
```

### IPolygonClientFactory
Creates and manages Polygon HTTP clients.

```csharp
public interface IPolygonClientFactory
{
    HttpClient CreateClient();
    Task<T> ExecuteRequestAsync<T>(string endpoint, CancellationToken cancellationToken = default);
}
```

## Safety and Monitoring

### ITradingSafetyGuard
Trading safety validation and checks.

```csharp
public interface ITradingSafetyGuard
{
    Task<SafetyCheckResult> ValidateTradingCycleAsync(CancellationToken cancellationToken = default);
    Task<SafetyCheckResult> ValidateTradeAsync(TradingSignal signal, decimal quantity, CancellationToken cancellationToken = default);
}
```

### IApiHealthMonitor
API connectivity and health monitoring.

```csharp
public interface IApiHealthMonitor
{
    Task<ApiHealthStatus> GetAlpacaHealthAsync();
    Task<ApiHealthStatus> GetPolygonHealthAsync();
    Task<OverallHealthStatus> GetOverallHealthAsync();
}
```

## Usage Examples

### Basic Trading Cycle
```csharp
// Inject ITradingService
var tradingService = serviceProvider.GetRequiredService<ITradingService>();

// Execute trading cycle
await tradingService.ExecuteCycleAsync();
```

### Market Data Retrieval
```csharp
// Get historical bars
var marketData = serviceProvider.GetRequiredService<IMarketDataService>();
var bars = await marketData.GetStockBarsAsync("AAPL", DateTime.Today.AddDays(-30), DateTime.Today);

// Get account information
var account = await marketData.GetAccountAsync();
Console.WriteLine($"Equity: {account.Equity:C}");
```

### Real-time Streaming
```csharp
var streaming = serviceProvider.GetRequiredService<IStreamingDataService>();

// Subscribe to events
streaming.QuoteReceived += (sender, e) => 
    Console.WriteLine($"{e.Symbol}: {e.BidPrice:C} x {e.AskPrice:C}");

// Connect and subscribe
await streaming.ConnectAlpacaStreamAsync();
await streaming.SubscribeToQuotesAsync(new[] { "SPY", "QQQ", "AAPL" });
```

## Error Handling

### Common Exception Types
- `AlpacaApiException`: Alpaca API-specific errors
- `PolygonApiException`: Polygon API-specific errors
- `TradingValidationException`: Trading rule violations
- `MarketDataException`: Data retrieval failures
- `CacheException`: Cache operation failures

### Retry Policies
All API calls implement exponential backoff retry policies:
- **Alpaca**: 3 retries with 200ms base delay
- **Polygon**: 3 retries with 200ms base delay
- **Database**: 2 retries with 100ms base delay

### Rate Limiting
- **Alpaca Markets**: 200 requests/minute
- **Polygon.io**: 5 requests/second
- **Automatic throttling**: Built-in rate limiting with queue management

## Configuration

### Environment Variables
```bash
# Required - Alpaca Trading API
APCA_API_KEY_ID=your_alpaca_key_id
APCA_API_SECRET_KEY=your_alpaca_secret_key
APCA_API_ENV=paper  # or live

# Optional - Polygon Market Data
POLY_API_KEY=your_polygon_api_key

# Optional - Redis Cache
REDIS_URL=localhost:6379
REDIS_DATABASE=0
REDIS_PASSWORD=your_redis_password

# Optional - Discord Notifications
DISCORD_BOT_TOKEN=your_discord_bot_token
DISCORD_CHANNEL_ID=your_discord_channel_id
```

### Service Registration
```csharp
// Register core services
services.AddSingleton<IAlpacaClientFactory, AlpacaClientFactory>();
services.AddSingleton<IMarketDataService, MarketDataService>();
services.AddScoped<ITradingService, TradingService>();
services.AddScoped<ISignalGenerator, SignalGenerator>();
services.AddScoped<IRiskManager, RiskManager>();
services.AddScoped<ITradeExecutor, TradeExecutor>();
```

## Performance Considerations

### Caching Strategy
- **SQLite Cache**: Historical bars cached locally for 1 year
- **Redis Cache**: Live trading state with 24-hour TTL
- **Memory Cache**: Frequently accessed configuration data

### Optimization Tips
1. **Batch Operations**: Use multi-symbol API calls when possible
2. **Cache Warming**: Pre-load cache before market open
3. **Connection Pooling**: Reuse HTTP connections
4. **Async Operations**: All I/O operations are asynchronous

### Memory Management
- Implement `IDisposable` for all services with resources
- Use `using` statements for short-lived objects
- Monitor memory usage in production environments

## Testing

### Unit Test Examples
```csharp
[Fact]
public async Task SignalGenerator_ShouldReturnValidSignals()
{
    // Arrange
    var mockMarketData = new Mock<IMarketDataService>();
    var signalGenerator = new SignalGenerator(mockMarketData.Object, logger);

    // Act
    var signals = await signalGenerator.RunAsync(5);

    // Assert
    signals.Should().NotBeEmpty();
    signals.Should().HaveCountLessOrEqualTo(5);
}
```

### Integration Test Setup
```csharp
public class TradingServiceIntegrationTests : IClassFixture<TestFixture>
{
    private readonly IServiceProvider _serviceProvider;

    public TradingServiceIntegrationTests(TestFixture fixture)
    {
        _serviceProvider = fixture.ServiceProvider;
    }

    [Fact]
    public async Task ExecuteCycleAsync_ShouldCompleteSuccessfully()
    {
        var tradingService = _serviceProvider.GetRequiredService<ITradingService>();
        await tradingService.ExecuteCycleAsync();
    }
}
```

## Security Best Practices

### API Key Security
- Store API keys in environment variables only
- Use separate keys for paper and live trading
- Rotate keys regularly
- Monitor API key usage

### Network Security
- Use HTTPS for all API communications
- Implement certificate validation
- Consider VPN for production deployments

### Data Protection
- Encrypt sensitive data at rest
- Use secure connection strings
- Implement proper access controls

This comprehensive API reference provides all necessary information for developing with and extending the SmaTrendFollower system.
