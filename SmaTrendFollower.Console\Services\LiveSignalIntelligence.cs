using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace SmaTrendFollower.Services;

/// <summary>
/// Live signal intelligence system that continuously analyzes market conditions,
/// generates real-time trading signals, and adapts to changing market dynamics.
/// </summary>
public sealed class LiveSignalIntelligence : BackgroundService, ILiveSignalIntelligence
{
    private readonly ISignalGenerator _signalGenerator;
    private readonly IRealTimeMarketMonitor _marketMonitor;
    private readonly ILiveStateStore _liveStateStore;
    private readonly ILogger<LiveSignalIntelligence> _logger;
    private readonly SignalIntelligenceConfig _config;

    private readonly ConcurrentDictionary<string, LiveSignalState> _signalStates = new();
    private readonly ConcurrentQueue<IntelligentSignal> _signalQueue = new();
    private readonly SemaphoreSlim _analysisLock = new(1, 1);

    // Events for real-time signal notifications
    public event EventHandler<SignalGeneratedEventArgs>? SignalGenerated;
    public event EventHandler<SignalUpdatedEventArgs>? SignalUpdated;
    public event EventHandler<MarketRegimeChangedEventArgs>? MarketRegimeChanged;

    public LiveSignalIntelligence(
        ISignalGenerator signalGenerator,
        IRealTimeMarketMonitor marketMonitor,
        ILiveStateStore liveStateStore,
        ILogger<LiveSignalIntelligence> logger,
        SignalIntelligenceConfig? config = null)
    {
        _signalGenerator = signalGenerator;
        _marketMonitor = marketMonitor;
        _liveStateStore = liveStateStore;
        _logger = logger;
        _config = config ?? new SignalIntelligenceConfig();

        // Subscribe to market events
        _marketMonitor.MarketConditionChanged += OnMarketConditionChanged;
        _marketMonitor.SignificantPriceMovement += OnSignificantPriceMovement;
    }

    /// <summary>
    /// Gets current live signals
    /// </summary>
    public List<IntelligentSignal> GetLiveSignals()
    {
        return _signalQueue.TakeLast(_config.MaxLiveSignals).ToList();
    }

    /// <summary>
    /// Gets signal state for a symbol
    /// </summary>
    public LiveSignalState? GetSignalState(string symbol)
    {
        return _signalStates.TryGetValue(symbol, out var state) ? state : null;
    }

    /// <summary>
    /// Gets all active signal states
    /// </summary>
    public Dictionary<string, LiveSignalState> GetAllSignalStates()
    {
        return new Dictionary<string, LiveSignalState>(_signalStates);
    }

    /// <summary>
    /// Forces immediate signal analysis for specific symbols
    /// </summary>
    public async Task<List<IntelligentSignal>> AnalyzeSymbolsAsync(IEnumerable<string> symbols)
    {
        var results = new List<IntelligentSignal>();

        try
        {
            await _analysisLock.WaitAsync();

            foreach (var symbol in symbols)
            {
                var signal = await AnalyzeSymbolInternalAsync(symbol);
                if (signal != null)
                {
                    results.Add(signal);
                }
            }
        }
        finally
        {
            _analysisLock.Release();
        }

        return results;
    }

    /// <summary>
    /// Background service execution - continuous signal intelligence
    /// </summary>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("LiveSignalIntelligence started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await PerformIntelligentAnalysisAsync(stoppingToken);
                await UpdateSignalStatesAsync(stoppingToken);
                await ProcessSignalQueueAsync(stoppingToken);
                await MonitorMarketRegimeAsync(stoppingToken);
                
                await Task.Delay(_config.AnalysisInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in signal intelligence analysis");
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }

        _logger.LogInformation("LiveSignalIntelligence stopped");
    }

    /// <summary>
    /// Performs intelligent market analysis and signal generation
    /// </summary>
    private async Task PerformIntelligentAnalysisAsync(CancellationToken cancellationToken)
    {
        try
        {
            // Get current market condition
            var marketCondition = await GetCurrentMarketConditionAsync();
            
            // Adjust analysis based on market condition
            var analysisMode = DetermineAnalysisMode(marketCondition);
            
            // Generate signals based on current market regime
            var signals = await GenerateIntelligentSignalsAsync(analysisMode);
            
            foreach (var signal in signals)
            {
                await ProcessIntelligentSignalAsync(signal);
            }

            _logger.LogDebug("Completed intelligent analysis: {SignalCount} signals generated in {Mode} mode",
                signals.Count, analysisMode);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in intelligent analysis");
        }
    }

    /// <summary>
    /// Generates intelligent signals based on analysis mode
    /// </summary>
    private async Task<List<IntelligentSignal>> GenerateIntelligentSignalsAsync(AnalysisMode mode)
    {
        var signals = new List<IntelligentSignal>();

        try
        {
            // Get base signals from signal generator
            var baseSignals = await _signalGenerator.RunAsync(_config.MaxSignalsPerCycle);
            
            foreach (var baseSignal in baseSignals)
            {
                var intelligentSignal = await EnhanceSignalWithIntelligenceAsync(baseSignal, mode);
                if (intelligentSignal != null)
                {
                    signals.Add(intelligentSignal);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating intelligent signals");
        }

        return signals;
    }

    /// <summary>
    /// Enhances base signal with intelligence and market context
    /// </summary>
    private Task<IntelligentSignal?> EnhanceSignalWithIntelligenceAsync(TradingSignal baseSignal, AnalysisMode mode)
    {
        try
        {
            // Get market snapshot for the symbol
            var marketSnapshot = _marketMonitor.GetMarketSnapshot(baseSignal.Symbol);
            
            // Calculate intelligence score
            var intelligenceScore = CalculateIntelligenceScore(baseSignal, marketSnapshot, mode);
            
            // Determine signal confidence
            var confidence = CalculateSignalConfidence(baseSignal, marketSnapshot, intelligenceScore);
            
            // Check if signal meets minimum thresholds
            if (confidence < _config.MinimumSignalConfidence)
            {
                return Task.FromResult<IntelligentSignal?>(null);
            }

            // Create intelligent signal
            var intelligentSignal = new IntelligentSignal(
                baseSignal.Symbol,
                baseSignal.Price,
                baseSignal.Atr,
                baseSignal.SixMonthReturn,
                intelligenceScore,
                confidence,
                mode,
                marketSnapshot?.Trend ?? MarketTrend.Unknown,
                marketSnapshot?.Volatility ?? 0,
                DateTime.UtcNow,
                GenerateSignalReasoning(baseSignal, marketSnapshot, mode)
            );

            return Task.FromResult<IntelligentSignal?>(intelligentSignal);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error enhancing signal for {Symbol}", baseSignal.Symbol);
            return Task.FromResult<IntelligentSignal?>(null);
        }
    }

    /// <summary>
    /// Calculates intelligence score based on multiple factors
    /// </summary>
    private decimal CalculateIntelligenceScore(TradingSignal signal, MarketSnapshot? snapshot, AnalysisMode mode)
    {
        var score = 0.5m; // Base score

        // Momentum component (0-0.3)
        var momentumScore = Math.Min(0.3m, signal.SixMonthReturn * 0.5m);
        score += momentumScore;

        // Market trend alignment (0-0.2)
        if (snapshot != null)
        {
            var trendScore = snapshot.Trend switch
            {
                MarketTrend.Bullish => 0.2m,
                MarketTrend.Sideways => 0.1m,
                MarketTrend.Bearish => 0.0m,
                _ => 0.05m
            };
            score += trendScore;
        }

        // Volatility component (0-0.15)
        var atrPercent = signal.Price > 0 ? signal.Atr / signal.Price : 0;
        var volatilityScore = atrPercent switch
        {
            >= 0.01m and <= 0.03m => 0.15m, // Ideal volatility range
            >= 0.005m and <= 0.05m => 0.1m, // Acceptable range
            _ => 0.05m // Too high or too low
        };
        score += volatilityScore;

        // Analysis mode adjustment
        var modeAdjustment = mode switch
        {
            AnalysisMode.Conservative => score * 0.8m,
            AnalysisMode.Aggressive => score * 1.2m,
            AnalysisMode.Adaptive => score, // No adjustment
            _ => score * 0.9m
        };

        return Math.Max(0, Math.Min(1, modeAdjustment));
    }

    /// <summary>
    /// Calculates signal confidence based on various factors
    /// </summary>
    private decimal CalculateSignalConfidence(TradingSignal signal, MarketSnapshot? snapshot, decimal intelligenceScore)
    {
        var confidence = intelligenceScore;

        // Adjust for market volatility
        if (snapshot != null && snapshot.Volatility > 0.04m)
        {
            confidence *= 0.8m; // Reduce confidence in high volatility
        }

        // Adjust for price momentum
        if (signal.SixMonthReturn > 0.2m)
        {
            confidence *= 1.1m; // Boost confidence for strong momentum
        }

        return Math.Max(0, Math.Min(1, confidence));
    }

    /// <summary>
    /// Generates human-readable reasoning for the signal
    /// </summary>
    private string GenerateSignalReasoning(TradingSignal signal, MarketSnapshot? snapshot, AnalysisMode mode)
    {
        var reasons = new List<string>();

        if (signal.SixMonthReturn > 0.1m)
            reasons.Add($"Strong 6-month momentum ({signal.SixMonthReturn:P1})");

        if (snapshot?.Trend == MarketTrend.Bullish)
            reasons.Add("Bullish trend confirmed");

        var atrPercent = signal.Price > 0 ? signal.Atr / signal.Price : 0;
        if (atrPercent >= 0.01m && atrPercent <= 0.03m)
            reasons.Add("Optimal volatility range");

        reasons.Add($"Analysis mode: {mode}");

        return string.Join(", ", reasons);
    }

    /// <summary>
    /// Processes an intelligent signal
    /// </summary>
    private async Task ProcessIntelligentSignalAsync(IntelligentSignal signal)
    {
        try
        {
            // Update signal state
            var signalState = new LiveSignalState(
                signal.Symbol,
                signal,
                DateTime.UtcNow,
                SignalStatus.Active,
                new List<SignalUpdate>()
            );

            _signalStates[signal.Symbol] = signalState;

            // Enqueue signal
            _signalQueue.Enqueue(signal);

            // Limit queue size
            while (_signalQueue.Count > _config.MaxSignalQueueSize)
            {
                _signalQueue.TryDequeue(out _);
            }

            // Store in live state store using existing API
            await _liveStateStore.SetMarketStateAsync($"live_signal:{signal.Symbol}", signal, TimeSpan.FromHours(24));

            // Trigger event
            SignalGenerated?.Invoke(this, new SignalGeneratedEventArgs(signal));

            _logger.LogInformation("Generated intelligent signal: {Symbol} - Confidence={Confidence:F2}, Score={Score:F2}",
                signal.Symbol, signal.Confidence, signal.IntelligenceScore);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing intelligent signal for {Symbol}", signal.Symbol);
        }
    }

    /// <summary>
    /// Analyzes a single symbol internally
    /// </summary>
    private async Task<IntelligentSignal?> AnalyzeSymbolInternalAsync(string symbol)
    {
        try
        {
            // Generate a signal for this specific symbol
            var signals = await _signalGenerator.RunAsync(1);
            var baseSignal = signals.FirstOrDefault(s => s.Symbol == symbol);

            if (baseSignal.Symbol == null)
                return null;

            var marketCondition = await GetCurrentMarketConditionAsync();
            var analysisMode = DetermineAnalysisMode(marketCondition);
            
            return await EnhanceSignalWithIntelligenceAsync(baseSignal, analysisMode);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error analyzing symbol {Symbol}", symbol);
            return null;
        }
    }

    /// <summary>
    /// Updates existing signal states
    /// </summary>
    private async Task UpdateSignalStatesAsync(CancellationToken cancellationToken)
    {
        var statesToUpdate = _signalStates.Values
            .Where(s => s.Status == SignalStatus.Active)
            .Where(s => DateTime.UtcNow - s.LastUpdated > _config.SignalUpdateInterval)
            .ToList();

        foreach (var state in statesToUpdate)
        {
            try
            {
                await UpdateSignalStateAsync(state);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error updating signal state for {Symbol}", state.Symbol);
            }
        }
    }

    /// <summary>
    /// Updates a single signal state
    /// </summary>
    private Task UpdateSignalStateAsync(LiveSignalState state)
    {
        var snapshot = _marketMonitor.GetMarketSnapshot(state.Symbol);
        if (snapshot == null)
            return Task.CompletedTask;

        // Check if signal is still valid
        var isStillValid = IsSignalStillValid(state.CurrentSignal, snapshot);
        
        var newStatus = isStillValid ? SignalStatus.Active : SignalStatus.Expired;
        
        var update = new SignalUpdate(
            DateTime.UtcNow,
            snapshot.CurrentPrice,
            newStatus,
            $"Price: {snapshot.CurrentPrice:F2}, Trend: {snapshot.Trend}"
        );

        var updatedState = state with
        {
            LastUpdated = DateTime.UtcNow,
            Status = newStatus,
            Updates = state.Updates.Concat(new[] { update }).ToList()
        };

        _signalStates[state.Symbol] = updatedState;

        if (newStatus != state.Status)
        {
            SignalUpdated?.Invoke(this, new SignalUpdatedEventArgs(state.Symbol, state.Status, newStatus));
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// Checks if a signal is still valid based on current market conditions
    /// </summary>
    private bool IsSignalStillValid(IntelligentSignal signal, MarketSnapshot snapshot)
    {
        // Signal expires if price has moved significantly against the trend
        var priceChange = (snapshot.CurrentPrice - signal.Price) / signal.Price;
        
        if (priceChange < -0.05m) // 5% drop
            return false;

        // Signal expires if trend has reversed
        if (snapshot.Trend == MarketTrend.Bearish)
            return false;

        // Signal expires if volatility has spiked
        if (snapshot.Volatility > 0.06m) // 6% volatility
            return false;

        return true;
    }

    /// <summary>
    /// Processes the signal queue
    /// </summary>
    private async Task ProcessSignalQueueAsync(CancellationToken cancellationToken)
    {
        // Implementation for processing queued signals
        // This could include sending notifications, updating databases, etc.
        await Task.CompletedTask;
    }

    /// <summary>
    /// Monitors market regime changes
    /// </summary>
    private async Task MonitorMarketRegimeAsync(CancellationToken cancellationToken)
    {
        try
        {
            var currentCondition = await GetCurrentMarketConditionAsync();
            var previousConditionObj = await _liveStateStore.GetMarketStateAsync<object>("previous_market_condition");
            var previousCondition = previousConditionObj != null ? (MarketCondition)previousConditionObj : (MarketCondition?)null;

            if (previousCondition != currentCondition)
            {
                await _liveStateStore.SetMarketStateAsync("previous_market_condition", currentCondition, TimeSpan.FromDays(1));
                
                MarketRegimeChanged?.Invoke(this, new MarketRegimeChangedEventArgs(
                    previousCondition ?? MarketCondition.Normal, currentCondition));

                _logger.LogInformation("Market regime changed from {Previous} to {Current}",
                    previousCondition, currentCondition);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error monitoring market regime");
        }
    }

    /// <summary>
    /// Gets current market condition
    /// </summary>
    private async Task<MarketCondition> GetCurrentMarketConditionAsync()
    {
        var conditionObj = await _liveStateStore.GetMarketStateAsync<object>("market_condition");
        return conditionObj != null ? (MarketCondition)conditionObj : MarketCondition.Normal;
    }

    /// <summary>
    /// Determines analysis mode based on market condition
    /// </summary>
    private AnalysisMode DetermineAnalysisMode(MarketCondition condition)
    {
        return condition switch
        {
            MarketCondition.Calm => AnalysisMode.Aggressive,
            MarketCondition.Normal => AnalysisMode.Adaptive,
            MarketCondition.Elevated => AnalysisMode.Conservative,
            MarketCondition.Stressed => AnalysisMode.Conservative,
            MarketCondition.Crisis => AnalysisMode.Defensive,
            _ => AnalysisMode.Adaptive
        };
    }

    /// <summary>
    /// Handles market condition changes
    /// </summary>
    private void OnMarketConditionChanged(object? sender, MarketConditionEventArgs e)
    {
        _logger.LogInformation("Market condition changed to {Condition} (VIX: {VIX:F1})",
            e.Condition, e.VixLevel);
    }

    /// <summary>
    /// Handles significant price movements
    /// </summary>
    private void OnSignificantPriceMovement(object? sender, PriceMovementEventArgs e)
    {
        _logger.LogDebug("Significant price movement: {Symbol} {Change:P2}",
            e.Symbol, e.PercentChange);
    }

    public override void Dispose()
    {
        _analysisLock?.Dispose();
        base.Dispose();
    }
}

/// <summary>
/// Interface for live signal intelligence
/// </summary>
public interface ILiveSignalIntelligence
{
    event EventHandler<SignalGeneratedEventArgs>? SignalGenerated;
    event EventHandler<SignalUpdatedEventArgs>? SignalUpdated;
    event EventHandler<MarketRegimeChangedEventArgs>? MarketRegimeChanged;

    List<IntelligentSignal> GetLiveSignals();
    LiveSignalState? GetSignalState(string symbol);
    Dictionary<string, LiveSignalState> GetAllSignalStates();
    Task<List<IntelligentSignal>> AnalyzeSymbolsAsync(IEnumerable<string> symbols);
}

/// <summary>
/// Configuration for signal intelligence
/// </summary>
public record SignalIntelligenceConfig(
    TimeSpan AnalysisInterval = default,
    TimeSpan SignalUpdateInterval = default,
    int MaxSignalsPerCycle = 10,
    int MaxLiveSignals = 100,
    int MaxSignalQueueSize = 1000,
    decimal MinimumSignalConfidence = 0.6m
)
{
    public SignalIntelligenceConfig() : this(
        TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(2), 10, 100, 1000, 0.6m) { }
}

/// <summary>
/// Analysis modes for different market conditions
/// </summary>
public enum AnalysisMode
{
    Conservative,
    Adaptive,
    Aggressive,
    Defensive
}

/// <summary>
/// Signal status tracking
/// </summary>
public enum SignalStatus
{
    Active,
    Expired,
    Executed,
    Cancelled
}

/// <summary>
/// Intelligent signal with enhanced analysis
/// </summary>
public record IntelligentSignal(
    string Symbol,
    decimal Price,
    decimal Atr,
    decimal SixMonthReturn,
    decimal IntelligenceScore,
    decimal Confidence,
    AnalysisMode AnalysisMode,
    MarketTrend MarketTrend,
    decimal Volatility,
    DateTime GeneratedAt,
    string Reasoning
);

/// <summary>
/// Live signal state tracking
/// </summary>
public record LiveSignalState(
    string Symbol,
    IntelligentSignal CurrentSignal,
    DateTime LastUpdated,
    SignalStatus Status,
    List<SignalUpdate> Updates
);

/// <summary>
/// Signal update information
/// </summary>
public record SignalUpdate(
    DateTime Timestamp,
    decimal Price,
    SignalStatus Status,
    string Notes
);

/// <summary>
/// Event arguments for signal generation
/// </summary>
public record SignalGeneratedEventArgs(IntelligentSignal Signal);

/// <summary>
/// Event arguments for signal updates
/// </summary>
public record SignalUpdatedEventArgs(
    string Symbol,
    SignalStatus PreviousStatus,
    SignalStatus NewStatus
);

/// <summary>
/// Event arguments for market regime changes
/// </summary>
public record MarketRegimeChangedEventArgs(
    MarketCondition PreviousRegime,
    MarketCondition NewRegime
);
