using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text;

namespace SmaTrendFollower.Services;

/// <summary>
/// ChatGPT-powered NLP service for earnings summarization, sentiment scoring, and intelligent log generation
/// Adds intelligence layer to filters, journaling, and risk control using OpenAI GPT API
/// </summary>
public sealed class ChatGptNlpService : IChatGptNlpService, IDisposable
{
    private readonly HttpClient _httpClient;
    private readonly ILiveStateStore _liveStateStore;
    private readonly ILogger<ChatGptNlpService> _logger;
    private readonly ChatGptConfig _config;
    private readonly SemaphoreSlim _rateLimitSemaphore;

    // Cache for NLP results
    private readonly Dictionary<string, NlpResult> _resultCache = new();
    private DateTime _lastCacheCleanup = DateTime.UtcNow;

    public ChatGptNlpService(
        HttpClient httpClient,
        ILiveStateStore liveStateStore,
        IConfiguration configuration,
        ILogger<ChatGptNlpService> logger)
    {
        _httpClient = httpClient;
        _liveStateStore = liveStateStore;
        _logger = logger;
        
        var apiKey = configuration["OPENAI_API_KEY"] ?? throw new InvalidOperationException("OPENAI_API_KEY not configured");
        
        _config = new ChatGptConfig(
            ApiKey: apiKey,
            Model: configuration["OPENAI_MODEL"] ?? "gpt-3.5-turbo",
            MaxTokens: int.Parse(configuration["OPENAI_MAX_TOKENS"] ?? "1000"),
            Temperature: decimal.Parse(configuration["OPENAI_TEMPERATURE"] ?? "0.3"),
            RateLimitPerMinute: int.Parse(configuration["OPENAI_RATE_LIMIT_PER_MINUTE"] ?? "20"),
            CacheExpiry: TimeSpan.FromHours(int.Parse(configuration["NLP_CACHE_HOURS"] ?? "24"))
        );

        _httpClient.BaseAddress = new Uri("https://api.openai.com/v1/");
        _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_config.ApiKey}");
        _httpClient.DefaultRequestHeaders.Add("User-Agent", "SmaTrendFollower/1.0");

        // Rate limiting semaphore
        _rateLimitSemaphore = new SemaphoreSlim(_config.RateLimitPerMinute, _config.RateLimitPerMinute);
    }

    /// <summary>
    /// Summarizes earnings report or financial document
    /// </summary>
    public async Task<EarningsSummary> SummarizeEarningsAsync(string symbol, string earningsText, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"earnings_summary:{symbol}:{earningsText.GetHashCode()}";
            
            // Check cache first
            if (TryGetCachedResult<EarningsSummary>(cacheKey, out var cached))
            {
                return cached;
            }

            var prompt = CreateEarningsSummaryPrompt(symbol, earningsText);
            var response = await CallChatGptAsync(prompt, cancellationToken);
            
            var summary = ParseEarningsSummary(response, symbol);
            
            // Cache the result
            await CacheResultAsync(cacheKey, summary);
            
            _logger.LogInformation("Generated earnings summary for {Symbol}", symbol);
            return summary;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error summarizing earnings for {Symbol}", symbol);
            return new EarningsSummary(symbol, "Error processing earnings", SentimentScore.Neutral, new List<string>(), DateTime.UtcNow);
        }
    }

    /// <summary>
    /// Scores sentiment of news or financial text
    /// </summary>
    public async Task<SentimentAnalysis> AnalyzeSentimentAsync(string text, string? context = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"sentiment:{text.GetHashCode()}:{context?.GetHashCode()}";
            
            // Check cache first
            if (TryGetCachedResult<SentimentAnalysis>(cacheKey, out var cached))
            {
                return cached;
            }

            var prompt = CreateSentimentAnalysisPrompt(text, context);
            var response = await CallChatGptAsync(prompt, cancellationToken);
            
            var analysis = ParseSentimentAnalysis(response);
            
            // Cache the result
            await CacheResultAsync(cacheKey, analysis);
            
            _logger.LogDebug("Analyzed sentiment: {Score} confidence {Confidence:F2}", analysis.Score, analysis.Confidence);
            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing sentiment");
            return new SentimentAnalysis(SentimentScore.Neutral, 0.5m, "Error processing text", DateTime.UtcNow);
        }
    }

    /// <summary>
    /// Generates intelligent trading log entry
    /// </summary>
    public async Task<string> GenerateLogEntryAsync(string action, string symbol, decimal price, string reasoning, CancellationToken cancellationToken = default)
    {
        try
        {
            var prompt = CreateLogGenerationPrompt(action, symbol, price, reasoning);
            var response = await CallChatGptAsync(prompt, cancellationToken);
            
            var logEntry = ParseLogEntry(response);
            
            _logger.LogDebug("Generated intelligent log entry for {Action} {Symbol}", action, symbol);
            return logEntry;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating log entry for {Action} {Symbol}", action, symbol);
            return $"{DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} - {action} {symbol} @ ${price:F2} - {reasoning}";
        }
    }

    /// <summary>
    /// Extracts key insights from financial text
    /// </summary>
    public async Task<List<KeyInsight>> ExtractInsightsAsync(string text, string? symbol = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"insights:{text.GetHashCode()}:{symbol}";
            
            // Check cache first
            if (TryGetCachedResult<List<KeyInsight>>(cacheKey, out var cached))
            {
                return cached;
            }

            var prompt = CreateInsightExtractionPrompt(text, symbol);
            var response = await CallChatGptAsync(prompt, cancellationToken);
            
            var insights = ParseInsights(response);
            
            // Cache the result
            await CacheResultAsync(cacheKey, insights);
            
            _logger.LogInformation("Extracted {Count} insights from text", insights.Count);
            return insights;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting insights");
            return new List<KeyInsight>();
        }
    }

    /// <summary>
    /// Generates risk assessment commentary
    /// </summary>
    public async Task<string> GenerateRiskCommentaryAsync(string symbol, decimal volatility, decimal momentum, string marketCondition, CancellationToken cancellationToken = default)
    {
        try
        {
            var prompt = CreateRiskCommentaryPrompt(symbol, volatility, momentum, marketCondition);
            var response = await CallChatGptAsync(prompt, cancellationToken);
            
            var commentary = ParseRiskCommentary(response);
            
            _logger.LogDebug("Generated risk commentary for {Symbol}", symbol);
            return commentary;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating risk commentary for {Symbol}", symbol);
            return $"Standard risk assessment for {symbol} - Volatility: {volatility:F2}, Momentum: {momentum:F2}, Market: {marketCondition}";
        }
    }

    /// <summary>
    /// Calls ChatGPT API with rate limiting
    /// </summary>
    private async Task<string> CallChatGptAsync(string prompt, CancellationToken cancellationToken)
    {
        await _rateLimitSemaphore.WaitAsync(cancellationToken);
        
        try
        {
            var request = new ChatGptRequest
            {
                Model = _config.Model,
                Messages = new[]
                {
                    new ChatGptMessage { Role = "system", Content = "You are a financial analyst AI assistant. Provide concise, accurate analysis." },
                    new ChatGptMessage { Role = "user", Content = prompt }
                },
                MaxTokens = _config.MaxTokens,
                Temperature = _config.Temperature
            };

            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("chat/completions", content, cancellationToken);
            response.EnsureSuccessStatusCode();

            var responseJson = await response.Content.ReadAsStringAsync(cancellationToken);
            var chatResponse = JsonSerializer.Deserialize<ChatGptResponse>(responseJson);

            return chatResponse?.Choices?.FirstOrDefault()?.Message?.Content ?? "No response generated";
        }
        finally
        {
            // Release rate limit after delay
            _ = Task.Delay(TimeSpan.FromMinutes(1.0 / _config.RateLimitPerMinute), cancellationToken)
                .ContinueWith(_ => _rateLimitSemaphore.Release(), cancellationToken);
        }
    }

    /// <summary>
    /// Creates earnings summary prompt
    /// </summary>
    private string CreateEarningsSummaryPrompt(string symbol, string earningsText)
    {
        return $@"Analyze the following earnings report for {symbol} and provide a structured summary:

{earningsText.Substring(0, Math.Min(2000, earningsText.Length))}

Please provide:
1. Overall sentiment (Positive/Negative/Neutral)
2. Key highlights (3-5 bullet points)
3. Any red flags or concerns
4. Impact on stock price (likely direction)

Format as JSON with fields: sentiment, highlights, concerns, priceImpact";
    }

    /// <summary>
    /// Creates sentiment analysis prompt
    /// </summary>
    private string CreateSentimentAnalysisPrompt(string text, string? context)
    {
        var contextText = !string.IsNullOrEmpty(context) ? $"\nContext: {context}" : "";
        
        return $@"Analyze the sentiment of the following financial text:{contextText}

Text: {text.Substring(0, Math.Min(1000, text.Length))}

Provide sentiment score from -1.0 (very negative) to **** (very positive) and confidence level 0-1.
Format as JSON with fields: score, confidence, reasoning";
    }

    /// <summary>
    /// Creates log generation prompt
    /// </summary>
    private string CreateLogGenerationPrompt(string action, string symbol, decimal price, string reasoning)
    {
        return $@"Generate a professional trading log entry for:
Action: {action}
Symbol: {symbol}
Price: ${price:F2}
Reasoning: {reasoning}

Create a concise, informative log entry that includes timestamp, action details, and strategic rationale.
Keep it under 200 characters.";
    }

    /// <summary>
    /// Creates insight extraction prompt
    /// </summary>
    private string CreateInsightExtractionPrompt(string text, string? symbol)
    {
        var symbolText = !string.IsNullOrEmpty(symbol) ? $" related to {symbol}" : "";
        
        return $@"Extract key trading insights{symbolText} from the following text:

{text.Substring(0, Math.Min(1500, text.Length))}

Identify:
1. Market trends or patterns
2. Risk factors
3. Opportunities
4. Important dates or events

Format as JSON array with fields: type, description, importance (1-5), timeframe";
    }

    /// <summary>
    /// Creates risk commentary prompt
    /// </summary>
    private string CreateRiskCommentaryPrompt(string symbol, decimal volatility, decimal momentum, string marketCondition)
    {
        return $@"Generate risk assessment commentary for {symbol}:
- Volatility: {volatility:F2}
- Momentum: {momentum:F2}
- Market Condition: {marketCondition}

Provide a concise risk analysis (2-3 sentences) focusing on key risk factors and trading implications.";
    }

    /// <summary>
    /// Parses earnings summary from ChatGPT response
    /// </summary>
    private EarningsSummary ParseEarningsSummary(string response, string symbol)
    {
        try
        {
            var json = JsonDocument.Parse(response);
            var root = json.RootElement;

            var sentimentText = root.GetProperty("sentiment").GetString() ?? "Neutral";
            var sentiment = Enum.TryParse<SentimentScore>(sentimentText, true, out var s) ? s : SentimentScore.Neutral;
            
            var highlights = new List<string>();
            if (root.TryGetProperty("highlights", out var highlightsElement))
            {
                foreach (var highlight in highlightsElement.EnumerateArray())
                {
                    highlights.Add(highlight.GetString() ?? "");
                }
            }

            var summary = root.TryGetProperty("priceImpact", out var impactElement) 
                ? impactElement.GetString() ?? "Neutral impact expected"
                : "Neutral impact expected";

            return new EarningsSummary(symbol, summary, sentiment, highlights, DateTime.UtcNow);
        }
        catch
        {
            // Fallback parsing
            return new EarningsSummary(symbol, response, SentimentScore.Neutral, new List<string>(), DateTime.UtcNow);
        }
    }

    /// <summary>
    /// Parses sentiment analysis from ChatGPT response
    /// </summary>
    private SentimentAnalysis ParseSentimentAnalysis(string response)
    {
        try
        {
            var json = JsonDocument.Parse(response);
            var root = json.RootElement;

            var score = root.GetProperty("score").GetDecimal();
            var confidence = root.GetProperty("confidence").GetDecimal();
            var reasoning = root.GetProperty("reasoning").GetString() ?? "";

            var sentimentScore = score switch
            {
                <= -0.5m => SentimentScore.VeryNegative,
                <= -0.1m => SentimentScore.Negative,
                <= 0.1m => SentimentScore.Neutral,
                <= 0.5m => SentimentScore.Positive,
                _ => SentimentScore.VeryPositive
            };

            return new SentimentAnalysis(sentimentScore, confidence, reasoning, DateTime.UtcNow);
        }
        catch
        {
            return new SentimentAnalysis(SentimentScore.Neutral, 0.5m, response, DateTime.UtcNow);
        }
    }

    /// <summary>
    /// Parses log entry from ChatGPT response
    /// </summary>
    private string ParseLogEntry(string response)
    {
        // Clean up the response and ensure it's a proper log entry
        var cleaned = response.Trim().Replace("\n", " ").Replace("\"", "");
        
        // Ensure it starts with timestamp if not present
        if (!cleaned.Contains(DateTime.UtcNow.ToString("yyyy-MM-dd")))
        {
            cleaned = $"{DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} - {cleaned}";
        }

        return cleaned.Length > 300 ? cleaned.Substring(0, 300) + "..." : cleaned;
    }

    /// <summary>
    /// Parses insights from ChatGPT response
    /// </summary>
    private List<KeyInsight> ParseInsights(string response)
    {
        var insights = new List<KeyInsight>();

        try
        {
            var json = JsonDocument.Parse(response);
            
            foreach (var element in json.RootElement.EnumerateArray())
            {
                var type = element.GetProperty("type").GetString() ?? "General";
                var description = element.GetProperty("description").GetString() ?? "";
                var importance = element.GetProperty("importance").GetInt32();
                var timeframe = element.GetProperty("timeframe").GetString() ?? "Unknown";

                insights.Add(new KeyInsight(type, description, importance, timeframe, DateTime.UtcNow));
            }
        }
        catch
        {
            // Fallback: create a single insight from the response
            insights.Add(new KeyInsight("Analysis", response, 3, "General", DateTime.UtcNow));
        }

        return insights;
    }

    /// <summary>
    /// Parses risk commentary from ChatGPT response
    /// </summary>
    private string ParseRiskCommentary(string response)
    {
        return response.Trim().Replace("\n", " ");
    }

    /// <summary>
    /// Tries to get cached result
    /// </summary>
    private bool TryGetCachedResult<T>(string key, out T result)
    {
        result = default!;
        
        if (_resultCache.TryGetValue(key, out var cached) && 
            DateTime.UtcNow - cached.Timestamp < _config.CacheExpiry)
        {
            try
            {
                var deserialized = JsonSerializer.Deserialize<T>(cached.Data);
                if (deserialized != null)
                {
                    result = deserialized;
                    return true;
                }
            }
            catch
            {
                _resultCache.Remove(key);
            }
        }

        return false;
    }

    /// <summary>
    /// Caches result
    /// </summary>
    private async Task CacheResultAsync<T>(string key, T result)
    {
        try
        {
            var data = JsonSerializer.Serialize(result);
            _resultCache[key] = new NlpResult(data, DateTime.UtcNow);

            // Also store in live state store
            if (result != null)
            {
                await _liveStateStore.SetMarketStateAsync($"nlp_cache:{key}", result, _config.CacheExpiry);
            }

            // Cleanup old cache entries periodically
            if (DateTime.UtcNow - _lastCacheCleanup > TimeSpan.FromHours(1))
            {
                CleanupCache();
                _lastCacheCleanup = DateTime.UtcNow;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching NLP result");
        }
    }

    /// <summary>
    /// Cleans up old cache entries
    /// </summary>
    private void CleanupCache()
    {
        var cutoff = DateTime.UtcNow - _config.CacheExpiry;
        var keysToRemove = _resultCache
            .Where(kvp => kvp.Value.Timestamp < cutoff)
            .Select(kvp => kvp.Key)
            .ToList();

        foreach (var key in keysToRemove)
        {
            _resultCache.Remove(key);
        }
    }

    public void Dispose()
    {
        _rateLimitSemaphore?.Dispose();
        _httpClient?.Dispose();
    }
}

/// <summary>
/// Interface for ChatGPT NLP service
/// </summary>
public interface IChatGptNlpService
{
    Task<EarningsSummary> SummarizeEarningsAsync(string symbol, string earningsText, CancellationToken cancellationToken = default);
    Task<SentimentAnalysis> AnalyzeSentimentAsync(string text, string? context = null, CancellationToken cancellationToken = default);
    Task<string> GenerateLogEntryAsync(string action, string symbol, decimal price, string reasoning, CancellationToken cancellationToken = default);
    Task<List<KeyInsight>> ExtractInsightsAsync(string text, string? symbol = null, CancellationToken cancellationToken = default);
    Task<string> GenerateRiskCommentaryAsync(string symbol, decimal volatility, decimal momentum, string marketCondition, CancellationToken cancellationToken = default);
}

/// <summary>
/// Configuration for ChatGPT service
/// </summary>
public record ChatGptConfig(
    string ApiKey,
    string Model,
    int MaxTokens,
    decimal Temperature,
    int RateLimitPerMinute,
    TimeSpan CacheExpiry
);

/// <summary>
/// NLP result cache entry
/// </summary>
public record NlpResult(string Data, DateTime Timestamp);

/// <summary>
/// Earnings summary result
/// </summary>
public record EarningsSummary(
    string Symbol,
    string Summary,
    SentimentScore Sentiment,
    List<string> KeyHighlights,
    DateTime GeneratedAt
);

/// <summary>
/// Sentiment analysis result
/// </summary>
public record SentimentAnalysis(
    SentimentScore Score,
    decimal Confidence,
    string Reasoning,
    DateTime GeneratedAt
);

/// <summary>
/// Key insight extracted from text
/// </summary>
public record KeyInsight(
    string Type,
    string Description,
    int Importance,
    string Timeframe,
    DateTime ExtractedAt
);

/// <summary>
/// Sentiment score enumeration
/// </summary>
public enum SentimentScore
{
    VeryNegative = -2,
    Negative = -1,
    Neutral = 0,
    Positive = 1,
    VeryPositive = 2
}

/// <summary>
/// ChatGPT API request structure
/// </summary>
public class ChatGptRequest
{
    [JsonPropertyName("model")]
    public string Model { get; set; } = string.Empty;

    [JsonPropertyName("messages")]
    public ChatGptMessage[] Messages { get; set; } = Array.Empty<ChatGptMessage>();

    [JsonPropertyName("max_tokens")]
    public int MaxTokens { get; set; }

    [JsonPropertyName("temperature")]
    public decimal Temperature { get; set; }
}

/// <summary>
/// ChatGPT message structure
/// </summary>
public class ChatGptMessage
{
    [JsonPropertyName("role")]
    public string Role { get; set; } = string.Empty;

    [JsonPropertyName("content")]
    public string Content { get; set; } = string.Empty;
}

/// <summary>
/// ChatGPT API response structure
/// </summary>
public class ChatGptResponse
{
    [JsonPropertyName("choices")]
    public ChatGptChoice[]? Choices { get; set; }
}

/// <summary>
/// ChatGPT choice structure
/// </summary>
public class ChatGptChoice
{
    [JsonPropertyName("message")]
    public ChatGptMessage? Message { get; set; }
}
